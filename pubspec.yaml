name: zuz_app
description: "A new Flutter project."
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  collection: ^1.19.1
  cupertino_icons: ^1.0.8
  dio: ^5.8.0+1
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_secure_storage: ^9.2.4
  flutter_svg: ^2.2.0
  get_it: ^8.2.0
  go_router: ^16.1.0

dev_dependencies:
  build_runner: ^2.4.6
  flutter_lints: ^5.0.0
  flutter_native_splash: ^2.4.6
  flutter_test:
    sdk: flutter
  mocktail: ^1.0.4
  go_router_builder: ^3.0.1

flutter:
  assets:
    - assets/icons/
    - assets/images/
  fonts:
    - family: ZuzIcons
      fonts:
        - asset: assets/fonts/zuz_icons.ttf
  uses-material-design: true

  # Configuração de fontes
  # fonts:
  #   - family: Century Gothic
  #     fonts:
  #       - asset: assets/fonts/CenturyGothic.ttf
  #       - asset: assets/fonts/CenturyGothic-Bold.ttf
  #         weight: 700

flutter_native_splash:
  color: "#FFFFFF"
  android: true
  ios: true
  web: false
  android_12:
    color: "#FFFFFF"
