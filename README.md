# ZUZ App

Aplicativo Flutter desenvolvido seguindo uma arquitetura modular bem definida, com separação clara de responsabilidades entre as diferentes camadas.

## 📋 Índice

- [Arquitetura](#arquitetura)
- [Estrutura do Projeto](#estrutura-do-projeto)
- [Camadas dos Módulos](#camadas-dos-módulos)
- [Módulos Principais](#módulos-principais)
- [Testes](#testes)
- [Como Executar](#como-executar)
- [Desenvolvimento](#desenvolvimento)

## 🏗️ Arquitetura

O projeto é dividido em **módulos** e cada módulo possui diferentes **camadas**, permitindo separação de responsabilidades e facilitando a manutenção e testes.

### Princípios Arquiteturais

- **Modularidade**: Cada módulo tem uma pasta própria em `/lib`
- **Flexibilidade**: Módulos não precisam ter todas as camadas, apenas as que fizerem sentido
- **Organização**: Módulos podem ser agrupados em pastas para melhor organização
- **Exceções**: Alguns módulos podem ter estruturas específicas (como `components` e `utils`)

## 📁 Estrutura do Projeto

```
lib/
├── app/                    # Configurações e inicialização da aplicação
├── auth/                   # Autenticação e autorização
├── components/             # Componentes reutilizáveis
├── error/                  # Tratamento de erros
├── features/               # Funcionalidades específicas
├── http_client/            # Cliente HTTP
├── local_storage/          # Armazenamento local
├── localization/           # Internacionalização
├── locator/                # Injeção de dependências
├── navigation/             # Navegação entre telas
├── theme/                  # Temas e estilos
├── user_configuration/     # Configurações do usuário
├── utils/                  # Utilitários gerais
└── main.dart              # Ponto de entrada da aplicação
```

## 🔧 Camadas dos Módulos

Cada módulo pode conter as seguintes camadas, conforme necessário:

### **Services**
- **Responsabilidade**: Acesso a fontes de dados externas (APIs HTTP, dados do dispositivo)
- **Acesso**: Podem ser acessados apenas por repositories
- **Implementação**: Deve ser feita a partir de uma interface

### **Repositories**
- **Responsabilidade**: Fonte única da verdade (SSOT - Single Source Of Truth)
- **Função**: Têm acesso aos services para obtenção de dados e servem como fonte para os states
- **Implementação**: Deve ser feita a partir de uma interface

### **Models**
- **Responsabilidade**: Objetos que representam dados relevantes
- **Reutilização**: Podem ser reutilizados em outros módulos

### **States**
- **Responsabilidade**: Gerencia o estado de uma view
- **Função**: Obtém dados de repositories e atualiza as views
- **Implementação**: Deve ser feita a partir de uma interface

### **Views**
- **Responsabilidade**: Widgets representando telas ou componentes, responsáveis apenas pelo layout, sem alterar o estado
- **Função**: Obtêm informações a partir de states
- **Exceção**: Componentes podem ter estado interno, caso necessário

### **Builders**
- **Responsabilidade**: Conectam o state com a view
- **Benefício**: Evitam acoplamento entre state e view
- **Flexibilidade**: Permitem alterar a solução de gerenciamento de estado sem afetar as views

## 🎯 Módulos Principais

- **`app/`** - Configurações e inicialização da aplicação
- **`auth/`** - Autenticação e autorização
- **`components/`** - Componentes UI reutilizáveis
- **`error/`** - Tratamento centralizado de erros
- **`features/`** - Funcionalidades específicas do negócio
- **`http_client/`** - Configuração e gerenciamento de requisições HTTP
- **`local_storage/`** - Persistência de dados locais
- **`localization/`** - Suporte a múltiplos idiomas
- **`locator/`** - Injeção de dependências e service locator
- **`navigation/`** - Gerenciamento de rotas e navegação
- **`theme/`** - Temas, cores e estilos da aplicação
- **`user_configuration/`** - Configurações e preferências do usuário
- **`utils/`** - Utilitários e helpers gerais

## 📚 Documentação dos Módulos

- **App (`lib/app/`)**
  - Orquestra tema, navegação e i18n; expõe o widget raiz via `AppBuilder`.
  - Leia mais: [lib/app/README.md](lib/app/README.md)

- **Navigation (`lib/navigation/`)**
  - GoRouter com rotas type-safe geradas; repositório de navegação e boas práticas (`onExit`).
  - Leia mais: [lib/navigation/README.md](lib/navigation/README.md)

- **Locator (`lib/locator/`)**
  - Injeção de dependências com `GetIt` via `Locator`, registros centralizados em `initializeDependencies()`.
  - Leia mais: [lib/locator/README.md](lib/locator/README.md)

- **Localization (`lib/localization/`)**
  - Arquitetura de i18n com agregador `Strings` e `LocaleRepository` (locale atual, delegados, locais suportados).
  - Leia mais: [lib/localization/README.md](lib/localization/README.md)

- **Local Storage (`lib/local_storage/`)**
  - Persistência local segura com `flutter_secure_storage`, enum `LocalStorageKey` e `LocalStorageService`.
  - Leia mais: [lib/local_storage/README.md](lib/local_storage/README.md)

- **HTTP Client (`lib/http_client/`)**
  - Cliente HTTP baseado em `dio`, mapeamento de erros para `HttpError` (i18n com `HttpStrings`).
  - Leia mais: [lib/http_client/README.md](lib/http_client/README.md)

- **Error (`lib/error/`)**
  - Tratamento padronizado de erros com `AppError` e `ErrorHandlerState` (exibição + i18n).
  - Leia mais: [lib/error/README.md](lib/error/README.md)

## 🧪 Testes

### Estrutura de Testes

O projeto segue a mesma estrutura modular para os testes, mantendo a organização e facilitando a manutenção:

```
test/
├── auth/                   # Testes do módulo de autenticação
├── http_client/            # Testes do cliente HTTP
├── local_storage/          # Testes de armazenamento local
├── localization/           # Testes de internacionalização
├── locator/                # Testes de injeção de dependências
├── navigation/             # Testes de navegação
└── utils/                  # Testes de utilitários
```

### Tipos de Testes

#### **Testes Unitários**
- **Foco**: Services, Repositories, Models e States
- **Objetivo**: Testar a lógica de negócio isoladamente
- **Localização**: Cada módulo deve ter seus testes unitários correspondentes

#### **Testes de Widget**
- **Foco**: Views e componentes UI
- **Objetivo**: Verificar se os widgets são renderizados corretamente
- **Interação**: Testar interações do usuário com a interface

#### **Testes de Integração**
- **Foco**: Fluxos completos da aplicação
- **Objetivo**: Verificar se diferentes módulos funcionam juntos
- **Cenários**: Testar casos de uso end-to-end

### Boas Práticas de Testes

- **Cobertura**: Mantenha alta cobertura de testes, especialmente para Services e Repositories
- **Mocks**: Use mocks para isolar dependências externas (APIs, banco de dados)
- **Nomenclatura**: Use nomes descritivos que expliquem o cenário testado
- **Organização**: Agrupe testes relacionados usando `group()` e `describe()`
- **Setup**: Use `setUp()` e `tearDown()` para preparar e limpar o ambiente de teste

### Comandos de Teste

```bash
# Executar todos os testes
fvm flutter test

# Executar testes com cobertura
fvm flutter test --coverage

# Executar testes de um módulo específico
fvm flutter test test/auth/

# Executar um teste específico
fvm flutter test test/auth/repositories/auth_repository_test.dart
```

### Exemplo de Estrutura de Teste

```dart
// test/auth/repositories/auth_repository_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

void main() {
  group('AuthRepository', () {
    late AuthRepository repository;
    late MockAuthService mockService;

    setUp(() {
      mockService = MockAuthService();
      repository = AuthRepositoryImpl(mockService);
    });

    group('login', () {
      test('should return user when login is successful', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        
        // Act & Assert
        // ... implementação do teste
      });
    });
  });
}
```

## 🚀 Como Executar

### Pré-requisitos
- **FVM (Flutter Version Management)** - [https://fvm.app](https://fvm.app)
- Android Studio / VS Code
- Emulador ou dispositivo físico

> **Importante**: Este projeto utiliza FVM para gerenciar a versão do Flutter. O FVM garante que todos os desenvolvedores usem a mesma versão do Flutter, evitando problemas de compatibilidade.

### Instalação

1. **Instale o FVM** (se ainda não tiver):
```bash
# Via Homebrew (macOS)
brew tap leoafarias/fvm
brew install fvm

# Via Chocolatey (Windows)
choco install fvm

# Via pub global
dart pub global activate fvm
```

2. Clone o repositório:
```bash
git clone <url-do-repositorio>
cd zuz_app
```

3. **Configure o Flutter via FVM**:
```bash
# Instala a versão do Flutter especificada no .fvmrc
fvm install

# Usa a versão configurada para o projeto
fvm use
```

4. Instale as dependências:
```bash
fvm flutter pub get
```

5. Execute o aplicativo:
```bash
fvm flutter run
```

### Convenções

- Siga a arquitetura modular definida
- Implemente interfaces para Services, Repositories e States
- Mantenha a separação de responsabilidades entre as camadas
- Use builders para conectar states e views
- Documente código complexo e APIs públicas
