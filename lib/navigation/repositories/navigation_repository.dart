import 'package:flutter/widgets.dart';
import 'package:go_router/go_router.dart';

/// Contrato do repositório de navegação do app.
///
/// Fornece acesso ao `navigatorKey`, ao `context` atual, ao `routerConfig`
/// (GoRouter) e a um método de navegação type-safe via instâncias de
/// `GoRouteData` geradas pelo `go_router_builder`.
abstract class NavigationRepository {
  /// Chave global do `Navigator` usada pelo GoRouter.
  GlobalKey<NavigatorState>? get key;
  /// Contexto atual associado ao `navigatorKey`.
  BuildContext? get context;
  /// Navega para a rota representada por [route].
  void to(GoRouteData route);
  /// Configuração do GoRouter utilizada pelo app.
  RouterConfig<Object> get routerConfig;
}
