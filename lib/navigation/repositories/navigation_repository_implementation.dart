import 'package:flutter/widgets.dart';
import 'package:go_router/go_router.dart';

import '../../auth/login/repositories/login_repository.dart';
import '../../locator/locator.dart';
import '../models/account_route.dart' as account;
import '../models/assessments_route.dart' as assessments;
import '../models/herd_route.dart' as herd;
import '../models/onboarding_route.dart' as onboarding;
import '../models/packages_route.dart' as packages;
import '../models/properties_route.dart' as properties;
import '../models/team_route.dart' as team;
import '../models/navigation_error.dart';
import 'navigation_repository.dart';

/// Implementação padrão de `NavigationRepository` usando GoRouter.
///
/// - Mantém uma `GlobalKey<NavigatorState>` para recuperar `context`.
/// - Compõe as rotas geradas (`$appRoutes`) de cada módulo de features.
/// - Define `initialLocation` como a rota de onboarding.
/// - Expõe `routerConfig` para ser usado em `MaterialApp.router`.
class NavigationRepositoryImplementation extends NavigationRepository {
  final _key = GlobalKey<NavigatorState>();
  @override
  GlobalKey<NavigatorState>? get key => _key;

  @override
  BuildContext? get context => _key.currentContext;

  @override
  RouterConfig<Object> get routerConfig => GoRouter(
    navigatorKey: key,
    routes: [
      ...onboarding.$appRoutes,
      ...assessments.$appRoutes,
      ...herd.$appRoutes,
      ...properties.$appRoutes,
      ...packages.$appRoutes,
      ...team.$appRoutes,
      ...account.$appRoutes,
    ],
    initialLocation: onboarding.OnboardingRoute().location,
    redirect: (context, state) async {
      // Caso já esteja autenticado e a rota for de onboarding, redireciona para avaliações.
      final loginRepository = Locator.instance.get<LoginRepository>();
      if (state.matchedLocation == onboarding.OnboardingRoute().location) {
        if (await loginRepository.isAuthenticated) {
          return assessments.AssessmentsRoute().location;
        }
      }
      return null;
    },
  );

  @override
  /// Navega para a [route] informada usando o `context` do `navigatorKey`.
  ///
  /// Lança `NavigationError.nullContext` quando o contexto ainda não está
  /// disponível (ex.: antes da montagem da árvore de widgets).
  void to(GoRouteData route) {
    if (context == null) throw NavigationError.nullContext;
    route.go(context!);
  }
}
