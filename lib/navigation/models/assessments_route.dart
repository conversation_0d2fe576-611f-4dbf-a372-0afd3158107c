import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../features/assessments/builders/assessments_builder.dart';

part 'assessments_route.g.dart';

@TypedGoRoute<AssessmentsRoute>(path: '/avaliacoes')
class AssessmentsRoute extends GoRouteData with _$AssessmentsRoute {
  const AssessmentsRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const AssessmentsBuilder();
  }
}