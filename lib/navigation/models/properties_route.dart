import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../features/properties/builders/properties_builder.dart';

part 'properties_route.g.dart';

@TypedGoRoute<PropertiesRoute>(path: '/propriedades')
class PropertiesRoute extends GoRouteData with _$PropertiesRoute {
  const PropertiesRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const PropertiesBuilder();
  }
}
