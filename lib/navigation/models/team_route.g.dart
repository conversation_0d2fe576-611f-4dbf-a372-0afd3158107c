// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'team_route.dart';

// **************************************************************************
// GoRouterGenerator
// **************************************************************************

List<RouteBase> get $appRoutes => [$teamRoute];

RouteBase get $teamRoute =>
    GoRouteData.$route(path: '/minha-equipe', factory: _$TeamRoute._fromState);

mixin _$TeamRoute on GoRouteData {
  static TeamRoute _fromState(GoRouterState state) => const TeamRoute();

  @override
  String get location => GoRouteData.$location('/minha-equipe');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}
