import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../features/packages/builders/packages_builder.dart';

part 'packages_route.g.dart';

@TypedGoRoute<PackagesRoute>(path: '/pacotes')
class PackagesRoute extends GoRouteData with _$PackagesRoute {
  const PackagesRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const PackagesBuilder();
  }
}
