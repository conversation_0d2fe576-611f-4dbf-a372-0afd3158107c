// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account_route.dart';

// **************************************************************************
// GoRouterGenerator
// **************************************************************************

List<RouteBase> get $appRoutes => [$accountRoute];

RouteBase get $accountRoute => GoRouteData.$route(
  path: '/minha-conta',

  factory: _$AccountRoute._fromState,
);

mixin _$AccountRoute on GoRouteData {
  static AccountRoute _fromState(GoRouterState state) => const AccountRoute();

  @override
  String get location => GoRouteData.$location('/minha-conta');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}
