// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'properties_route.dart';

// **************************************************************************
// GoRouterGenerator
// **************************************************************************

List<RouteBase> get $appRoutes => [$propertiesRoute];

RouteBase get $propertiesRoute => GoRouteData.$route(
  path: '/propriedades',

  factory: _$PropertiesRoute._fromState,
);

mixin _$PropertiesRoute on GoRouteData {
  static PropertiesRoute _fromState(GoRouterState state) =>
      const PropertiesRoute();

  @override
  String get location => GoRouteData.$location('/propriedades');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}
