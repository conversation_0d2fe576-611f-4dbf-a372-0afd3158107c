import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:go_router/go_router.dart';
import 'package:zuz_app/auth/password_reset/builders/password_reset_builder.dart';

import '../../auth/first_access/builders/first_access_builder.dart';
import '../../auth/login/builders/email_password_login_builder.dart';
import '../../auth/login/states/email_password_login_state.dart';
import '../../auth/onboarding/builders/onboarding_builder.dart';
import '../../examples/components_examples_page.dart';
import '../../locator/locator.dart';

part 'onboarding_route.g.dart';

/// Rota raiz do fluxo de onboarding.
///
/// Definida de forma type-safe via `@TypedGoRoute` e `GoRouteData`, com
/// sub-rotas para login, primeiro acesso e alteração de senha.
@TypedGoRoute<OnboardingRoute>(
  path: '/onboarding',
  routes: [
    TypedGoRoute<LoginRoute>(
      path: 'login',
      routes: [TypedGoRoute<PasswordResetRoute>(path: 'alterar-senha')],
    ),
    TypedGoRoute<FirstAccessRoute>(path: 'primeiro-acesso'),
    TypedGoRoute<ComponentsExamplesRoute>(path: 'componentes'),
  ],
)
class OnboardingRoute extends GoRouteData with _$OnboardingRoute {
  const OnboardingRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const OnboardingBuilder();
  }
}

/// Rota de login por e-mail e senha.
///
/// Usa `onExit` para realizar limpeza de estado quando o usuário sai da rota,
/// por exemplo, resetando singletons lazy via service locator.
class LoginRoute extends GoRouteData with _$LoginRoute {
  const LoginRoute();

  @override
  /// Limpa o estado do fluxo de login antes de sair da rota.
  FutureOr<bool> onExit(BuildContext context, GoRouterState state) {
    Locator.instance.resetLazySingleton<EmailPasswordLoginState>();
    return true;
  }

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return EmailPasswordLoginBuilder();
  }
}

/// Rota de redefinição de senha dentro do fluxo de onboarding.
class PasswordResetRoute extends GoRouteData with _$PasswordResetRoute {
  const PasswordResetRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const PasswordResetBuilder();
  }
}

/// Rota de primeiro acesso dentro do fluxo de onboarding.
class FirstAccessRoute extends GoRouteData with _$FirstAccessRoute {
  const FirstAccessRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const FirstAccessBuilder();
  }
}

class ComponentsExamplesRoute extends GoRouteData with _$ComponentsExamplesRoute {
  const ComponentsExamplesRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const ComponentsExamplesPage();
  }
}
