// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'onboarding_route.dart';

// **************************************************************************
// GoRouterGenerator
// **************************************************************************

List<RouteBase> get $appRoutes => [$onboardingRoute];

RouteBase get $onboardingRoute => GoRouteData.$route(
  path: '/onboarding',

  factory: _$OnboardingRoute._fromState,
  routes: [
    GoRouteData.$route(
      path: 'login',

      factory: _$LoginRoute._fromState,
      routes: [
        GoRouteData.$route(
          path: 'alterar-senha',

          factory: _$PasswordResetRoute._fromState,
        ),
      ],
    ),
    GoRouteData.$route(
      path: 'primeiro-acesso',

      factory: _$FirstAccessRoute._fromState,
    ),
    GoRouteData.$route(
      path: 'componentes',

      factory: _$ComponentsExamplesRoute._fromState,
    ),
  ],
);

mixin _$OnboardingRoute on GoRouteData {
  static OnboardingRoute _fromState(GoRouterState state) =>
      const OnboardingRoute();

  @override
  String get location => GoRouteData.$location('/onboarding');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$LoginRoute on GoRouteData {
  static LoginRoute _fromState(GoRouterState state) => const LoginRoute();

  @override
  String get location => GoRouteData.$location('/onboarding/login');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$PasswordResetRoute on GoRouteData {
  static PasswordResetRoute _fromState(GoRouterState state) =>
      const PasswordResetRoute();

  @override
  String get location =>
      GoRouteData.$location('/onboarding/login/alterar-senha');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$FirstAccessRoute on GoRouteData {
  static FirstAccessRoute _fromState(GoRouterState state) =>
      const FirstAccessRoute();

  @override
  String get location => GoRouteData.$location('/onboarding/primeiro-acesso');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$ComponentsExamplesRoute on GoRouteData {
  static ComponentsExamplesRoute _fromState(GoRouterState state) =>
      const ComponentsExamplesRoute();

  @override
  String get location => GoRouteData.$location('/onboarding/componentes');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}
