// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'herd_route.dart';

// **************************************************************************
// GoRouterGenerator
// **************************************************************************

List<RouteBase> get $appRoutes => [$herdRoute];

RouteBase get $herdRoute =>
    GoRouteData.$route(path: '/rebanho', factory: _$HerdRoute._fromState);

mixin _$HerdRoute on GoRouteData {
  static HerdRoute _fromState(GoRouterState state) => const HerdRoute();

  @override
  String get location => GoRouteData.$location('/rebanho');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}
