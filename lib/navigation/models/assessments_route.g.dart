// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'assessments_route.dart';

// **************************************************************************
// GoRouterGenerator
// **************************************************************************

List<RouteBase> get $appRoutes => [$assessmentsRoute];

RouteBase get $assessmentsRoute => GoRouteData.$route(
  path: '/avaliacoes',

  factory: _$AssessmentsRoute._fromState,
);

mixin _$AssessmentsRoute on GoRouteData {
  static AssessmentsRoute _fromState(GoRouterState state) =>
      const AssessmentsRoute();

  @override
  String get location => GoRouteData.$location('/avaliacoes');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}
