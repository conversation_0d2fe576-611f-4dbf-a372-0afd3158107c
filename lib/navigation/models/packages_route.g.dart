// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'packages_route.dart';

// **************************************************************************
// GoRouterGenerator
// **************************************************************************

List<RouteBase> get $appRoutes => [$packagesRoute];

RouteBase get $packagesRoute =>
    GoRouteData.$route(path: '/pacotes', factory: _$PackagesRoute._fromState);

mixin _$PackagesRoute on GoRouteData {
  static PackagesRoute _fromState(GoRouterState state) => const PackagesRoute();

  @override
  String get location => GoRouteData.$location('/pacotes');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}
