import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../features/herd/builders/herd_builder.dart';

part 'herd_route.g.dart';

/// Rota da feature de rebanho (Herd), definida de forma type-safe
/// com `@TypedGoRoute` e `GoRouteData`.
@TypedGoRoute<HerdRoute>(path: '/rebanho')
class HerdRoute extends GoRouteData with _$HerdRoute {
  const HerdRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const HerdBuilder();
  }
}
