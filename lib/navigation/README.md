# Mó<PERSON>lo `navigation`

Gerencia a navegação do app usando o GoRouter com rotas type-safe (codegen) via `go_router_builder`. As rotas são definidas como classes que herdam de `GoRouteData`, o que permite navegação fortemente tipada e centralização de regras como `onExit`.

Referência: https://pub.dev/documentation/go_router_builder/latest/

## Princípios

- Rotas type-safe: cada rota é uma classe que estende `GoRouteData` e declara `build()` e opcionalmente `onExit()`.
- Codegen do GoRouter: os arquivos `*.g.dart` são gerados a partir das anotações `@TypedGoRoute`.
- Repositório de navegação: `NavigationRepository` abstrai a navegação, mantendo `navigatorKey`, `context` e `routerConfig`.
- Encapsulamento: `NavigationRepositoryImplementation` compõe as rotas geradas (`$appRoutes`) e define a `initialLocation`.
- Limpeza de estado por rota: use `onExit()` em uma `GoRouteData` para resetar instâncias com `resetLazySingleton` quando a rota é abandonada.
 - Arquivos de rota por ramo: cada arquivo em `models/` anotado com `@TypedGoRoute` representa um ramo paralelo a partir da raiz (ex.: `onboarding_route.dart`, `account_route.dart`). Cada um deve declarar todas as sub-rotas possíveis daquele ramo (via `routes: [...]`).

## Estrutura

- `repositories/navigation_repository.dart`: contrato com `key`, `context`, `to()` e `routerConfig`.
- `repositories/navigation_repository_implementation.dart`: implementação que monta o `GoRouter` e navega com `GoRouteData.go(context)`.
- `models/*_route.dart`: definição type-safe de rotas com `@TypedGoRoute` e `GoRouteData`.
- `models/*_route.g.dart`: arquivos gerados pelo `go_router_builder`.
- `models/navigation_error.dart`: erros de navegação (ex.: `nullContext`).

## Exemplo de rota type-safe

```dart
@TypedGoRoute<OnboardingRoute>(
  path: '/onboarding',
  routes: [
    TypedGoRoute<LoginRoute>(path: 'login'),
  ],
)
class OnboardingRoute extends GoRouteData {
  const OnboardingRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const OnboardingBuilder();
}
```

## onExit para limpeza de estado

Use `onExit` em uma rota para executar lógica ao sair, como resetar singletons lazy com o service locator.

```dart
class LoginRoute extends GoRouteData {
  const LoginRoute();

  @override
  FutureOr<bool> onExit(BuildContext context, GoRouterState state) {
    Locator.instance.resetLazySingleton<EmailPasswordLoginState>();
    return true; // permite sair
  }
}
```

## Navegação via repositório

A navegação deve ocorrer através do `NavigationRepository` para manter o acoplamento baixo.

```dart
navigationRepository.to(OnboardingRoute());
```

## Boas práticas

- Evite navegar diretamente com `context.go()` em States/Views; prefira o repositório.
- Agrupe sub-rotas relacionadas sob uma rota base (ex.: onboarding/login).
- Use `onExit` para limpar estados específicos do fluxo (ex.: forms de login).
- Mantenha rotas simples e focadas no builder de UI; lógica de negócios deve ficar nos States/Repositories.
