# ZuzButton - Bot<PERSON> Padronizado do Design System

`ZuzButton` é o componente único de botão da aplicação. Ele centraliza regras de layout, cores, tipografia, interações e acessibilidade conforme o Design System Zuz.

## Características

### Layout (Base)
- Display: Flex (Row)
- Padding padrão (primary / secondary / destructive): 14px vertical, 20px horizontal
- Variants icon / iconDestructive: padding 8px (quadrado)
- Variant tertiary: sem padding (inline)
- Alinhamento: center / center
- Gap texto↔ícone: 4px (quando ambos presentes)
- Border radius: 4px (todas as variantes com background ou contorno)
- Altura padrão: 48px (exceto icon/iconDestructive e tertiary que ajustam ao conteúdo)

### Estados (por variante)

| Variante | Normal / Loading | Hover | Pressed / Focus | Disabled |
|----------|------------------|-------|-----------------|----------|
| primary | Fundo `primary`, texto branco | Fundo igual + focus ring (box-shadows) | Fundo `primaryPressed` | Fundo `gray6`, texto branco |
| secondary | Fundo branco, borda cinza-4, texto `primary` | Mesmo fundo + focus ring | Fundo `gray1`, texto `primary` | Fundo `gray6`, texto branco |
| destructive | Fundo `error`, texto branco | Focus ring (com cor de erro) | Fundo `errorTexts` | Fundo `gray6`, texto branco |
| tertiary (text-only) | Texto `primary` | Texto `primary` com text-shadow verde | Texto `primaryPressed` | Texto `gray6` |
| icon | Fundo branco, borda cinza-2, ícone preto | Fundo `gray0`, borda cinza-3 + sombra | Fundo `gray3`, borda cinza-3 | Fundo branco, borda cinza-2, ícone `gray6` |
| iconDestructive | Fundo branco, borda cinza-2, ícone `error` | Fundo `errorBoxes`, borda vermelho translúcido + sombra | Fundo `errorBoxes`, borda `error` | Fundo branco, ícone `gray6` |

Notas:
- Loading reutiliza o estado visual "normal" de cada variante (ícone substituído por loader quando aplicável).
- Focus ring = conjunto de sombras definido no DS (interno leve + anel branco + anel de cor). Aplicado em hover para primary / secondary / destructive.

## Como Usar

### Importação
```dart
import 'components/button/button.dart';
```

### Uso Básico
```dart
ZuzButton(
  text: 'Meu Botão',
  onPressed: () {
    // ação
  },
)
```

### Com Ícone
```dart
ZuzButton(
  text: 'Salvar',
  icon: Icons.save,
  onPressed: () {},
)
```

### Estado de Loading
```dart
ZuzButton(
  text: 'Enviando...',
  isLoading: true,
  onPressed: () {}, // ignorado enquanto loading
)
```

### Botão Desabilitado
```dart
ZuzButton(
  text: 'Indisponível',
  onPressed: null, // null => disabled
)
```

### Largura Personalizada
```dart
ZuzButton(
  text: 'Botão Largo',
  width: double.infinity,
  onPressed: () {},
)
```

### Variantes

Primary (padrão):
```dart
ZuzButton(text: 'Continuar', onPressed: () {});
```

Secondary:
```dart
ZuzButton(
  text: 'Opção',
  variant: ZuzButtonVariant.secondary,
  onPressed: () {},
)
```

Tertiary (link-like, inline):
```dart
ZuzButton(
  text: 'Ver mais',
  variant: ZuzButtonVariant.tertiary,
  onPressed: () {},
)
```

Destructive:
```dart
ZuzButton(
  text: 'Excluir',
  variant: ZuzButtonVariant.destructive,
  icon: Icons.delete,
  onPressed: () {},
)
```

Icon (apenas ícone):
```dart
ZuzButton(
  text: 'Editar', // usado para acessibilidade / tooltip sem mostrar (não some do widget tree)
  variant: ZuzButtonVariant.icon,
  icon: Icons.edit,
  onPressed: () {},
)
```

Icon Destructive:
```dart
ZuzButton(
  text: 'Remover',
  variant: ZuzButtonVariant.iconDestructive,
  icon: Icons.close,
  onPressed: () {},
)
```

## Propriedades

| Propriedade | Tipo | Obrigatório | Padrão | Descrição |
|-------------|------|-------------|--------|-----------|
| `text` | `String` | ✅ | — | Texto visível (ou base sem exibição em variantes só-ícone / loading tertiary) |
| `onPressed` | `VoidCallback?` | ❌ | `null` | Callback de ação; `null` desabilita o botão |
| `isLoading` | `bool` | ❌ | `false` | Exibe loader e inibe `onPressed` |
| `icon` | `IconData?` | ❌ | — | Ícone à esquerda do texto ou único (icon/iconDestructive) |
| `width` | `double?` | ❌ | `null` | Largura fixa; `null` = ajusta ao conteúdo ou expande no layout pai (ex: `Expanded`) |
| `height` | `double?` | ❌ | `48` (exceto variants icon / iconDestructive / tertiary) | Altura externa do container animado |
| `textStyle` | `TextStyle?` | ❌ | Tipografia DS | Permite sobrescrever tipografia (cor é recalculada) |
| `variant` | `ZuzButtonVariant` | ❌ | `primary` | Define visual / comportamento de espaçamento |

## Comportamentos

### Interatividade
- Hover (web/desktop) com alteração de sombra / cor
- Press / Focus com estados dedicados (GestureDetector + Focus)
- Animação suave (150ms, `easeInOut`) em cor, sombra e borda
- Loader substitui ícone e mantém alinhamento

### Acessibilidade
- Semântica de botão preservada
- Navegação por teclado (Focus) com estado visual
- Texto ainda fornecido em variantes só-ícone para leitores (pode-se ocultar visualmente se necessário)

### Performance
- `AnimatedContainer` para transições mínimas
- Estado interno isolado (_hover / _pressed / _focused)
- Nenhum listener global / side effects

## Exemplos Avançados

### Botão de Ação Principal (Full Width)
```dart
ZuzButton(
  text: 'Finalizar Compra',
  icon: Icons.shopping_cart,
  width: double.infinity,
  onPressed: _finalizarCompra,
)
```

### Botão Secundário
```dart
ZuzButton(
  text: 'Cancelar',
  variant: ZuzButtonVariant.secondary,
  onPressed: () => Navigator.pop(context),
)
```

### Botão Condicional com Loading
```dart
ZuzButton(
  text: _isProcessing ? 'Processando...' : 'Enviar',
  isLoading: _isProcessing,
  onPressed: _isProcessing ? null : _enviarDados,
)
```

### Apenas Ícone (Destrutivo)
```dart
ZuzButton(
  text: 'Remover Item', // acessibilidade
  variant: ZuzButtonVariant.iconDestructive,
  icon: Icons.delete_forever,
  onPressed: _remover,
)
```

## Boas Práticas

- Prefira `Expanded(child: ZuzButton(...))` para ocupar largura flex.
- Evite aplicar múltiplos `GestureDetector` ancestrais (pode conflitar com estados pressed).
- Para tooltips em botões de ícone, use `Tooltip(message: 'Editar', child: ZuzButton(...))`.
- Para testes, valide estados via `tester.widget<ZuzButton>(find.byType(ZuzButton))`.

## Próximas Extensões (Roadmap)
- Suporte a estados de foco separado visual no teclado (caso o DS especifique divergência de hover)
- Variante ghost / outline adicional se o DS evoluir
- Suporte a ícone à direita (prop futura `iconPosition`)

---
Manter este documento alinhado ao código-fonte (`button.dart`) ao alterar lógica de variantes ou tokens.
