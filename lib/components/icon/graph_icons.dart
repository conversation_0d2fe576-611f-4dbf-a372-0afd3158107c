import 'package:flutter/widgets.dart';
import 'package:zuz_app/components/image/svg_asset.dart';

class GraphAnimalIcon extends StatelessWidget {
  const GraphAnimalIcon({super.key});

  @override
  Widget build(BuildContext context) {
    return SvgAsset('assets/icons/graph_animal.svg');
  }
}

class GraphAverageIcon extends StatelessWidget {
  const GraphAverageIcon({super.key});

  @override
  Widget build(BuildContext context) {
    return SvgAsset('assets/icons/graph_average.svg');
  }
}

class GraphZuzIcon extends StatelessWidget {
  const GraphZuzIcon({super.key});

  @override
  Widget build(BuildContext context) {
    return SvgAsset('assets/icons/graph_zuz.svg');
  }
}
