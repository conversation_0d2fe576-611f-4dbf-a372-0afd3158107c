import 'package:flutter/widgets.dart';

/// Ícones do design system. Use como `ZuzIcons.add`, similar a `Icons.add`.
///
/// Ícones gerados e obtidos de arquivos SVG do design e transformados em fonte
/// para uso como `IconData`.
///
/// Nem todos os ícones estão disponíveis, alguns não foram convertidos por
/// questões de compatibilidade.
///
/// Instruções para alterar os ícones:
/// 1. Importe o arquivo `assets/fonts/zuz_icons.ttf` em https://www.fluttericon.com.
/// 2. Adicione ou remova os ícones desejados e deixe selecionados apenas os que devem ser gerados.
/// 3. Faça download da fonte gerada. O download contém um .zip com o arquivo da fonte, um arquivo .dart e um arquivo .json (esse não é necessário).
/// 4. Substitua o arquivo da fonte pelo novo.
/// 5. Atualize as linhas necessárias deste arquivo para incluir/remover ícones ou substitua este arquivo pelo .dart gerado, ajustando o nome das constantes onde necessário.
abstract class ZuzIcons {
  static const _kFontFam = 'ZuzIcons';
  static const String? _kFontPkg = null;

  static const IconData add = IconData(
    0xe800,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData arrowDown = IconData(
    0xe801,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData arrowLeft = IconData(
    0xe802,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData arrowRightAlt = IconData(
    0xe803,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData arrowRight = IconData(
    0xe804,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData arrowUpDouble = IconData(
    0xe805,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData arrowUp = IconData(
    0xe806,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData assessmentAge = IconData(
    0xe807,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData assessmentEstimateIndividual = IconData(
    0xe808,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData assessmentEstimateLot = IconData(
    0xe809,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData assessmentZuzId = IconData(
    0xe80a,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData birthday = IconData(
    0xe80b,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData bulletListItem = IconData(
    0xe80c,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData calendarAge = IconData(
    0xe80d,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData calendarFilter = IconData(
    0xe80e,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData cancel = IconData(
    0xe80f,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData cattle = IconData(
    0xe810,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData checkFilled = IconData(
    0xe811,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData checkOutlined = IconData(
    0xe812,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData close = IconData(
    0xe813,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData database = IconData(
    0xe814,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData deleteEnterprise = IconData(
    0xe815,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData deleteUser = IconData(
    0xe816,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData delete = IconData(
    0xe817,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData download = IconData(
    0xe818,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData edit = IconData(
    0xe819,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData enterprise = IconData(
    0xe81a,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData error = IconData(
    0xe81b,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData exportIcon = IconData(
    0xe81c,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData file = IconData(
    0xe81d,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData filter = IconData(
    0xe81e,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData info = IconData(
    0xe822,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData logoU = IconData(
    0xe823,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData logout = IconData(
    0xe824,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData menu = IconData(
    0xe825,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData more = IconData(
    0xe826,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData order = IconData(
    0xe827,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData password = IconData(
    0xe828,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData profile = IconData(
    0xe829,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData warning = IconData(
    0xe82a,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData weight = IconData(
    0xe82b,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
}
