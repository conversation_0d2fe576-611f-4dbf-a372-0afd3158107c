import 'package:flutter/material.dart';

import '../../auth/login/repositories/login_repository.dart';
import '../../locator/locator.dart';
import '../../navigation/models/account_route.dart';
import '../../navigation/models/assessments_route.dart';
import '../../navigation/models/herd_route.dart';
import '../../navigation/models/onboarding_route.dart';
import '../../navigation/models/packages_route.dart';
import '../../navigation/models/properties_route.dart';
import '../../navigation/models/team_route.dart';
import '../../navigation/repositories/navigation_repository.dart';
import '../../theme/colors.dart';

class SideMenu extends StatelessWidget {
  const SideMenu({super.key});

  NavigationRepository get _navigationRepository =>
      Locator.instance.get<NavigationRepository>();

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: const BoxDecoration(color: AppColors.primary),
            child: Container(),
          ),
          ListTile(
            title: const Text('Avaliações'),
            onTap: () => _navigationRepository.to(AssessmentsRoute()),
          ),
          ListTile(
            title: const Text('Rebanho'),
            onTap: () => _navigationRepository.to(HerdRoute()),
          ),
          ListTile(
            title: const Text('Propriedades'),
            onTap: () => _navigationRepository.to(PropertiesRoute()),
          ),
          ListTile(
            title: const Text('Pacotes'),
            onTap: () => _navigationRepository.to(PackagesRoute()),
          ),
          ListTile(
            title: const Text('Minha Equipe'),
            onTap: () => _navigationRepository.to(TeamRoute()),
          ),
          ListTile(
            title: const Text('Minha Conta'),
            onTap: () => _navigationRepository.to(AccountRoute()),
          ),
          ListTile(
            title: const Text('Sair'),
            onTap: () {
              final loginRepository = Locator.instance.get<LoginRepository>();
              loginRepository.logout();
              _navigationRepository.to(OnboardingRoute());
            },
          ),
        ],
      ),
    );
  }
}
