import 'package:flutter/material.dart';

import '../../theme/colors.dart';
import '../top_bar/zuz_logo_top_bar.dart';
import 'background_pattern_decoration.dart';

class ScaffoldWithCenteredBody extends StatelessWidget {
  /// Cria um Scaffold com [topBar] customizada.
  const ScaffoldWithCenteredBody({
    super.key,
    required this.topBar,
    this.backgroundColor = AppColors.white,
    this.showPatternOverBackground = false,
    this.hortizontalBodyAndFooterPadding = 24,
    required this.body,
    this.bodyFooterSpacing = 40,
    required this.footer,
  });

  /// Cria um Scaffold com logo grande no topo.
  ScaffoldWithCenteredBody.largeLogo({
    super.key,
    this.backgroundColor = AppColors.gray2,
    this.showPatternOverBackground = true,
    this.hortizontalBodyAndFooterPadding = 24,
    required this.body,
    this.bodyFooterSpacing = 40,
    required this.footer,
  }) : topBar = ZuzLogoTopBar.large();

  /// Cria um Scaffold com logo pequeno no topo.
  ScaffoldWithCenteredBody.smallLogo({
    super.key,
    this.backgroundColor = AppColors.gray2,
    this.showPatternOverBackground = true,
    this.hortizontalBodyAndFooterPadding = 24,
    required this.body,
    this.bodyFooterSpacing = 40,
    required this.footer,
  }) : topBar = ZuzLogoTopBar.small();

  final AppBar topBar;
  final Color backgroundColor;

  /// Quando `true`, mostra imagem `assets/images/background_pattern.png` por cima da cor de fundo.
  final bool showPatternOverBackground;

  /// Espaçamento horizontal aplicado ao [body] e [footer].
  final double hortizontalBodyAndFooterPadding;

  /// Widget verticalmente centralizada. Permite rolagem junto ao [footer] quando conteúdo ultrapassa limite vertical da tela.
  final Widget body;

  /// Espaçamento entre [body] e [footer].
  final double bodyFooterSpacing;

  /// Widget fixo no rodapé. Permite rolagem junto ao [body] quando conteúdo ultrapassa limite vertical da tela.
  final Widget footer;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: showPatternOverBackground ? BackgroundPatternDecoration() : null,
      child: Scaffold(
        backgroundColor: showPatternOverBackground
            ? Colors.transparent
            : backgroundColor,
        appBar: topBar,
        body: LayoutBuilder(
          builder: (_, constraints) {
            return SingleChildScrollView(
              padding: EdgeInsets.symmetric(
                horizontal: hortizontalBodyAndFooterPadding,
              ),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight:
                      constraints.maxHeight -
                      MediaQuery.of(context).viewInsets.bottom,
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(height: MediaQuery.of(context).viewPadding.top),
                    body,
                    Padding(
                      padding: EdgeInsets.only(
                        top: bodyFooterSpacing,
                        bottom: MediaQuery.of(context).viewPadding.bottom,
                      ),
                      child: footer,
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
