import 'package:flutter/material.dart';
import '../../theme/colors.dart';

/// Tipo de design do ZuzTabBar
enum ZuzTabBarType {
  /// Padrão com linha indicadora em baixo (sem border radius)
  underline,
  /// Com background na tab selecionada
  background,
}

/// Tipo de dimensionamento da largura das tabs
enum ZuzTabBarSizing {
  /// Todas as tabs têm a mesma largura (dividem o espaço igualmente)
  uniform,
  /// Largura adaptativa ao conteúdo de cada tab
  adaptive,
}

/// TabBar personalizada que remove o border radius da linha indicadora
class ZuzTabBar extends StatelessWidget implements PreferredSizeWidget {
  /// Lista de tabs
  final List<Widget> tabs;
  
  /// Controlador das tabs
  final TabController? controller;
  
  /// Se as tabs devem ser scrolláveis
  final bool isScrollable;
  
  /// Cor da linha indicadora
  final Color? indicatorColor;
  
  /// Cor das tabs selecionadas
  final Color? labelColor;
  
  /// Cor das tabs não selecionadas
  final Color? unselectedLabelColor;
  
  /// Estilo do texto das tabs selecionadas
  final TextStyle? labelStyle;
  
  /// Estilo do texto das tabs não selecionadas
  final TextStyle? unselectedLabelStyle;
  
  /// Padding das tabs
  final EdgeInsetsGeometry? labelPadding;
  
  /// Callback quando uma tab é tocada
  final ValueChanged<int>? onTap;
  
  /// Tipo de design do TabBar
  final ZuzTabBarType type;
  
  /// Tipo de dimensionamento da largura das tabs
  final ZuzTabBarSizing sizing;

  const ZuzTabBar({
    super.key,
    required this.tabs,
    this.controller,
    this.isScrollable = false,
    this.indicatorColor,
    this.labelColor,
    this.unselectedLabelColor,
    this.labelStyle,
    this.unselectedLabelStyle,
    this.labelPadding,
    this.onTap,
    this.type = ZuzTabBarType.underline,
    this.sizing = ZuzTabBarSizing.uniform,
  });

  @override
  Size get preferredSize => const Size.fromHeight(kTextTabBarHeight);

  @override
  Widget build(BuildContext context) {
    if (type == ZuzTabBarType.background) {
      return _buildBackgroundTabBar(context);
    }
    
    // Tipo padrão com linha indicadora
    return TabBar(
      controller: controller,
      tabs: tabs,
      isScrollable: _shouldBeScrollable(),
      tabAlignment: _getTabAlignment(),
      indicatorColor: indicatorColor,
      labelColor: labelColor,
      unselectedLabelColor: unselectedLabelColor,
      labelStyle: labelStyle,
      unselectedLabelStyle: unselectedLabelStyle,
      labelPadding: labelPadding,
      onTap: onTap,
      // A única mudança: indicador sem border radius
      indicator: UnderlineTabIndicator(
        borderSide: BorderSide(
          color: indicatorColor ?? Theme.of(context).primaryColor,
          width: 2.0,
        ),
        // Sem border radius = bordas quadradas
      ),
    );
  }
  
  Widget _buildBackgroundTabBar(BuildContext context) {
    return TabBar(
      controller: controller,
      tabs: tabs,
      isScrollable: _shouldBeScrollable(),
      tabAlignment: _getTabAlignment(),
      labelColor: labelColor ?? AppColors.primary, // Brand Primary
      unselectedLabelColor: unselectedLabelColor,
      labelStyle: labelStyle ?? const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w700,
        height: 21 / 14, // lineHeight 21sp / fontSize 14sp
      ),
      unselectedLabelStyle: unselectedLabelStyle ?? const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        height: 21 / 14,
      ),
      labelPadding: labelPadding,
      onTap: onTap,
      // Remove o indicador padrão
      indicatorColor: Colors.transparent,
      indicator: _BackgroundTabIndicator(
        backgroundColor: AppColors.gray0, // Neutral Gray 0
      ),
    );
  }
  
  /// Determina se o TabBar deve ser scrollável baseado no tipo de sizing
  bool _shouldBeScrollable() {
    // Se o usuário especificou isScrollable explicitamente, respeita a escolha
    if (isScrollable) return true;
    
    // Para sizing adaptativo, usa scrollable para permitir tamanhos diferentes
    return sizing == ZuzTabBarSizing.adaptive;
  }
  
  /// Determina o alinhamento das tabs baseado no tipo de sizing
  TabAlignment? _getTabAlignment() {
    // Para sizing uniforme e não scrollable, preenche todo o espaço
    if (sizing == ZuzTabBarSizing.uniform && !isScrollable) {
      return TabAlignment.fill;
    }
    
    // Para sizing adaptativo, usa alinhamento de início
    if (sizing == ZuzTabBarSizing.adaptive) {
      return TabAlignment.start;
    }
    
    return null; // Deixa o padrão do Flutter decidir
  }
}

/// Indicador personalizado com background na tab selecionada
class _BackgroundTabIndicator extends Decoration {
  final Color backgroundColor;

  const _BackgroundTabIndicator({
    required this.backgroundColor,
  });

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _BackgroundTabIndicatorPainter(backgroundColor: backgroundColor);
  }
}

/// Painter para desenhar o background da tab selecionada
class _BackgroundTabIndicatorPainter extends BoxPainter {
  final Color backgroundColor;

  _BackgroundTabIndicatorPainter({
    required this.backgroundColor,
  });

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    final size = configuration.size!;
    
    // Usa o tamanho real da tab com um pequeno padding interno
    const horizontalPadding = -16; //Importante: Manter negativo, senão o background fica menor e cortado.
    const verticalPadding = 6.0;
    
    final width = size.width - (horizontalPadding * 2);
    final height = size.height - (verticalPadding * 2);
    
    // Centraliza o background na tab
    final x = offset.dx + horizontalPadding;
    final y = offset.dy + verticalPadding;
    
    final rect = Rect.fromLTWH(x, y, width, height);
    
    final paint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.fill;

    // Desenha o background com border radius de 6dp
    final rrect = RRect.fromRectAndRadius(rect, const Radius.circular(6.0));
    canvas.drawRRect(rrect, paint);
  }
}
