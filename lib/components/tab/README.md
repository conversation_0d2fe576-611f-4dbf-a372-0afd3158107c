# ZuzTabBar

TabBar personalizada com dois tipos de design disponíveis.

## Tipos de Design

### 1. Underline (Padrão)
Mantém o estilo padrão do Material Design mas remove o border radius da linha indicadora inferior.

### 2. Background
Tab selecionada com background cinza arredondado ao invés de linha indicadora.

## Tipos de Dimensionamento

### 1. Uniform (Padrão)
Todas as tabs têm a mesma largura, dividindo o espaço disponível igualmente.

### 2. Adaptive
Largura adaptativa ao conteúdo de cada tab (tamanho relativo ao texto).

## Uso Básico

### Tipo Underline (padrão)
```dart
DefaultTabController(
  length: 3,
  child: Scaffold(
    appBar: AppBar(
      bottom: ZuzTabBar(
        tabs: [
          Tab(text: 'Tab 1'),
          Tab(text: 'Tab 2'),
          Tab(text: 'Tab 3'),
        ],
      ),
    ),
    body: TabBarView(
      children: [
        Center(child: Text('Conteúdo 1')),
        Center(child: Text('Conteúdo 2')),
        Center(child: Text('Conteúdo 3')),
      ],
    ),
  ),
)
```

### Tipo Background
```dart
DefaultTabController(
  length: 3,
  child: Scaffold(
    appBar: AppBar(
      bottom: ZuzTabBar(
        type: ZuzTabBarType.background,
        tabs: [
          Tab(text: 'Tab 1'),
          Tab(text: 'Tab 2'),
          Tab(text: 'Tab 3'),
        ],
      ),
    ),
    body: TabBarView(
      children: [
        Center(child: Text('Conteúdo 1')),
        Center(child: Text('Conteúdo 2')),
        Center(child: Text('Conteúdo 3')),
      ],
    ),
  ),
)
```

### Tipo Background com Sizing Adaptive
```dart
DefaultTabController(
  length: 3,
  child: Scaffold(
    appBar: AppBar(
      bottom: ZuzTabBar(
        type: ZuzTabBarType.background,
        sizing: ZuzTabBarSizing.adaptive,
        tabs: [
          Tab(text: 'Tab'),
          Tab(text: 'Tab com texto maior'),
          Tab(text: 'Settings'),
        ],
      ),
    ),
    body: TabBarView(
      children: [
        Center(child: Text('Conteúdo 1')),
        Center(child: Text('Conteúdo 2')),
        Center(child: Text('Conteúdo 3')),
      ],
    ),
  ),
)
```

## Características

### Tipo Underline
- ✅ Mantém o estilo visual padrão do Material Design
- ✅ Remove o border radius da linha indicadora (bordas quadradas)
- ✅ Linha indicadora com 2px de espessura

### Tipo Background
- ✅ Background na tab selecionada (adapta-se ao tamanho da tab)
- ✅ Cor do background: #EFF4F5 (Neutral Gray 0)
- ✅ Border radius de 6dp no background
- ✅ Padding interno de 8dp horizontal e 6dp vertical
- ✅ Texto bold (700) na tab selecionada
- ✅ Cor do texto selecionado: #78942B (Brand Primary)
- ✅ Sem linha indicadora

### Sizing Uniform
- ✅ Todas as tabs têm a mesma largura
- ✅ Dividem o espaço disponível igualmente
- ✅ Ideal para tabs com textos similares

### Sizing Adaptive
- ✅ Largura adaptativa ao conteúdo
- ✅ Tabs menores ficam menores, maiores ficam maiores
- ✅ Ideal para tabs com textos de tamanhos muito diferentes
- ✅ Automaticamente torna-se scrollável quando necessário

## Parâmetros

- `type`: Define o tipo de design (`ZuzTabBarType.underline` ou `ZuzTabBarType.background`)
- `sizing`: Define o tipo de dimensionamento (`ZuzTabBarSizing.uniform` ou `ZuzTabBarSizing.adaptive`)
- `tabs`: Lista de widgets Tab padrão do Material
- `controller`: TabController para controle programático
- `isScrollable`: Define se as tabs podem fazer scroll
- `indicatorColor`: Cor da linha indicadora (apenas para tipo underline)
- `labelColor`: Cor do texto/ícone da tab selecionada
- `unselectedLabelColor`: Cor do texto/ícone da tab não selecionada
- `labelStyle`: Estilo do texto da tab selecionada
- `unselectedLabelStyle`: Estilo do texto da tab não selecionada
- `onTap`: Callback quando uma tab é tocada

## Estilos de Texto (Tipo Background)

### Tab Selecionada
- Font Size: 14sp
- Line Height: 21sp (150%)
- Font Weight: 700 (Bold)
- Color: #78942B (Brand Primary)

### Tab Não Selecionada
- Font Size: 14sp
- Line Height: 21sp (150%)
- Font Weight: 400 (Regular)
- Color: Cor padrão do tema
