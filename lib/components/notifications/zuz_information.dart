import 'package:flutter/material.dart';
import '../../theme/theme.dart';

/// Tipos de information disponíveis
enum ZuzInformationType {
  positive,
  warning,
  error,
  neutral,
}

/// Widget de information para exibir mensagens informativas no layout
/// Diferente do toast, este componente é inserido diretamente no layout
class ZuzInformation extends StatelessWidget {
  /// Tipo do information (positive, warning, error, neutral)
  final ZuzInformationType type;
  
  /// Mensagem principal do information
  final String message;
  
  /// Mensagem secundária (informações extras)
  final String? subtitle;
  
  /// Callback quando o botão de fechar é pressionado
  final VoidCallback? onClose;
  
  /// Se deve mostrar o botão de fechar
  final bool showCloseButton;

  const ZuzInformation({
    super.key,
    required this.type,
    required this.message,
    this.subtitle,
    this.onClose,
    this.showCloseButton = true,
  });

  /// Information positivo (success)
  factory ZuzInformation.positive({
    Key? key,
    required String message,
    String? subtitle,
    VoidCallback? onClose,
    bool showCloseButton = true,
  }) {
    return ZuzInformation(
      key: key,
      type: ZuzInformationType.positive,
      message: message,
      subtitle: subtitle,
      onClose: onClose,
      showCloseButton: showCloseButton,
    );
  }

  /// Information de warning
  factory ZuzInformation.warning({
    Key? key,
    required String message,
    String? subtitle,
    VoidCallback? onClose,
    bool showCloseButton = true,
  }) {
    return ZuzInformation(
      key: key,
      type: ZuzInformationType.warning,
      message: message,
      subtitle: subtitle,
      onClose: onClose,
      showCloseButton: showCloseButton,
    );
  }

  /// Information de erro
  factory ZuzInformation.error({
    Key? key,
    required String message,
    String? subtitle,
    VoidCallback? onClose,
    bool showCloseButton = true,
  }) {
    return ZuzInformation(
      key: key,
      type: ZuzInformationType.error,
      message: message,
      subtitle: subtitle,
      onClose: onClose,
      showCloseButton: showCloseButton,
    );
  }

  /// Information neutro
  factory ZuzInformation.neutral({
    Key? key,
    required String message,
    String? subtitle,
    VoidCallback? onClose,
    bool showCloseButton = true,
  }) {
    return ZuzInformation(
      key: key,
      type: ZuzInformationType.neutral,
      message: message,
      subtitle: subtitle,
      onClose: onClose,
      showCloseButton: showCloseButton,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: _getBackgroundColor(),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: IntrinsicHeight(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Ícone (se houver)
                if (_getIcon() != null) ...[
                  Padding(
                    padding: const EdgeInsets.only(top: 2),
                    child: Icon(
                      _getIcon()!,
                      size: 20,
                      color: _getIconColor(),
                    ),
                  ),
                  const SizedBox(width: 12),
                ],
                
                // Column com textos
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Mensagem principal
                      Text(
                        message,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: _getTextColor(),
                        ),
                      ),
                      
                      // Informações extras (se fornecidas)
                      if (subtitle != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          subtitle!,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: _getTextColor(),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                
                // Botão de fechar
                if (showCloseButton) ...[
                  const SizedBox(width: 12),
                  Padding(
                    padding: const EdgeInsets.only(top: 2),
                    child: GestureDetector(
                      onTap: onClose,
                      child: Icon(
                        Icons.close,
                        size: 20,
                        color: _getTextColor(),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Retorna o ícone baseado no tipo (null para neutral)
  IconData? _getIcon() {
    switch (type) {
      case ZuzInformationType.positive:
        return Icons.check_circle;
      case ZuzInformationType.warning:
        return Icons.warning;
      case ZuzInformationType.error:
        return Icons.cancel;
      case ZuzInformationType.neutral:
        return null; // Sem ícone
    }
  }

  /// Retorna a cor do ícone baseado no tipo
  Color _getIconColor() {
    switch (type) {
      case ZuzInformationType.positive:
        return AppColors.success;
      case ZuzInformationType.warning:
        return AppColors.warning;
      case ZuzInformationType.error:
        return AppColors.error;
      case ZuzInformationType.neutral:
        return AppColors.systemInfo; // Não usado, mas por consistência
    }
  }

  /// Retorna a cor do texto baseado no tipo
  Color _getTextColor() {
    switch (type) {
      case ZuzInformationType.positive:
        return AppColors.successTexts;
      case ZuzInformationType.warning:
        return AppColors.warningTexts;
      case ZuzInformationType.error:
        return AppColors.errorTexts;
      case ZuzInformationType.neutral:
        return AppColors.systemInfoTexts;
    }
  }

  /// Retorna a cor de fundo baseado no tipo
  Color _getBackgroundColor() {
    switch (type) {
      case ZuzInformationType.positive:
        return AppColors.successBoxes;
      case ZuzInformationType.warning:
        return AppColors.warningBoxes;
      case ZuzInformationType.error:
        return AppColors.errorBoxes;
      case ZuzInformationType.neutral:
        return AppColors.systemInfoBoxes;
    }
  }
}
