import 'package:flutter/material.dart';
import '../../theme/theme.dart';

/// Tipos de toast disponíveis
enum ZuzToastType {
  success,
  error,
}

/// Widget de toast para notificações flutuantes
/// Aparece na área inferior da tela com mensagem de sucesso ou erro
class ZuzToast extends StatefulWidget {
  /// Tipo do toast (success ou error)
  final ZuzToastType type;
  
  /// Mensagem principal do toast
  final String message;
  
  /// Mensagem secundária (informações extras)
  final String? subtitle;
  
  /// Callback quando o botão de fechar é pressionado
  final VoidCallback? onClose;
  
  /// Se deve mostrar o botão de fechar
  final bool showCloseButton;
  
  /// Se deve mostrar a animação de entrada
  final bool animate;

  const ZuzToast({
    super.key,
    required this.type,
    required this.message,
    this.subtitle,
    this.onClose,
    this.showCloseButton = true,
    this.animate = true,
  });

  /// Toast de sucesso
  factory ZuzToast.success({
    Key? key,
    required String message,
    String? subtitle,
    VoidCallback? onClose,
    bool showCloseButton = true,
    bool animate = true,
  }) {
    return ZuzToast(
      key: key,
      type: ZuzToastType.success,
      message: message,
      subtitle: subtitle,
      onClose: onClose,
      showCloseButton: showCloseButton,
      animate: animate,
    );
  }

  /// Toast de erro
  factory ZuzToast.error({
    Key? key,
    required String message,
    String? subtitle,
    VoidCallback? onClose,
    bool showCloseButton = true,
    bool animate = true,
  }) {
    return ZuzToast(
      key: key,
      type: ZuzToastType.error,
      message: message,
      subtitle: subtitle,
      onClose: onClose,
      showCloseButton: showCloseButton,
      animate: animate,
    );
  }

  @override
  State<ZuzToast> createState() => _ZuzToastState();
}

class _ZuzToastState extends State<ZuzToast> 
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  
  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1), // Começa de baixo
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    if (widget.animate) {
      _animationController.forward();
    } else {
      _animationController.value = 1.0;
    }
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  /// Anima a saída do toast
  Future<void> animateOut() async {
    await _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    Widget toastContent = Material(
      color: Colors.transparent,
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 416,
        ),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            // Primeira sombra (elevation = 6dp)
            BoxShadow(
              color: AppColors.black.withOpacity(0.05), // spotColor com opacity
              offset: Offset(0, 3),
              blurRadius: 6,
              spreadRadius: 0,
            ),
            // Segunda sombra (elevation = 15dp)
            BoxShadow(
              color: AppColors.black.withOpacity(0.1), // spotColor com opacity
              offset: Offset(0, 8),
              blurRadius: 15,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: IntrinsicHeight(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Ícone
                Padding(
                  padding: const EdgeInsets.only(top: 2),
                  child: Icon(
                    _getIcon(),
                    size: 20,
                    color: _getIconColor(),
                  ),
                ),
                
                const SizedBox(width: 12),
                
                // Column com textos
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Mensagem principal
                      Text(
                        widget.message,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: AppColors.black,
                        ),
                      ),
                      
                      // Informações extras (se fornecidas)
                      if (widget.subtitle != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          widget.subtitle!,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: AppColors.gray5,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                
                // Botão de fechar
                if (widget.showCloseButton) ...[
                  const SizedBox(width: 12),
                  Padding(
                    padding: const EdgeInsets.only(top: 2),
                    child: GestureDetector(
                      onTap: widget.onClose,
                      child: const Icon(
                        Icons.close,
                        size: 20,
                        color: AppColors.gray5,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
    
    if (!widget.animate) {
      return toastContent;
    }
    
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: toastContent,
      ),
    );
  }

  /// Retorna o ícone baseado no tipo
  IconData _getIcon() {
    switch (widget.type) {
      case ZuzToastType.success:
        return Icons.check_circle;
      case ZuzToastType.error:
        return Icons.cancel;
    }
  }

  /// Retorna a cor do ícone baseado no tipo
  Color _getIconColor() {
    switch (widget.type) {
      case ZuzToastType.success:
        return AppColors.success;
      case ZuzToastType.error:
        return AppColors.error;
    }
  }
}

/// Widget para exibir toasts de forma flutuante na tela
/// Gerencia a posição e animação dos toasts
class ZuzToastOverlay extends StatelessWidget {
  /// Lista de toasts para exibir
  final List<ZuzToast> toasts;
  
  /// Padding da área inferior
  final double bottomPadding;

  const ZuzToastOverlay({
    super.key,
    required this.toasts,
    this.bottomPadding = 24,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: bottomPadding,
      left: 0,
      right: 0,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: toasts
            .map((toast) => Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 8,
                  ),
                  child: toast,
                ))
            .toList(),
      ),
    );
  }
}

/// Método utilitário para mostrar toast usando Overlay
class ZuzToastManager {
  static OverlayEntry? _currentEntry;
  static ZuzToast? _currentToast;

  /// Mostra um toast na tela
  static void showToast(
    BuildContext context, {
    required ZuzToastType type,
    required String message,
    String? subtitle,
    Duration duration = const Duration(seconds: 4),
    VoidCallback? onClose,
  }) {
    // Remove o toast anterior se existir
    _removeCurrentToast();

    final overlay = Overlay.of(context);
    
    // Cria uma chave global para acessar o estado do toast
    final GlobalKey<_ZuzToastState> toastKey = GlobalKey<_ZuzToastState>();
    
    _currentToast = ZuzToast(
      key: toastKey,
      type: type,
      message: message,
      subtitle: subtitle,
      onClose: () async {
        // Anima a saída antes de remover
        await toastKey.currentState?.animateOut();
        onClose?.call();
        _removeCurrentToast();
      },
    );
    
    _currentEntry = OverlayEntry(
      builder: (context) => ZuzToastOverlay(
        toasts: [_currentToast!],
      ),
    );

    overlay.insert(_currentEntry!);

    // Remove automaticamente após a duração especificada
    Future.delayed(duration, () async {
      if (_currentEntry != null && toastKey.currentState != null) {
        // Anima a saída antes de remover
        await toastKey.currentState!.animateOut();
        _removeCurrentToast();
      }
    });
  }

  /// Mostra um toast de sucesso
  static void showSuccess(
    BuildContext context, {
    required String message,
    String? subtitle,
    Duration duration = const Duration(seconds: 4),
    VoidCallback? onClose,
  }) {
    showToast(
      context,
      type: ZuzToastType.success,
      message: message,
      subtitle: subtitle,
      duration: duration,
      onClose: onClose,
    );
  }

  /// Mostra um toast de erro
  static void showError(
    BuildContext context, {
    required String message,
    String? subtitle,
    Duration duration = const Duration(seconds: 4),
    VoidCallback? onClose,
  }) {
    showToast(
      context,
      type: ZuzToastType.error,
      message: message,
      subtitle: subtitle,
      duration: duration,
      onClose: onClose,
    );
  }

  /// Remove o toast atual
  static void _removeCurrentToast() {
    _currentEntry?.remove();
    _currentEntry = null;
    _currentToast = null;
  }

  /// Remove todos os toasts
  static void clear() {
    _removeCurrentToast();
  }
}
