# Notifications Components

Este diretório contém os componentes de notificação do aplicativo: **Toast** e **Information**.

## ZuzToast

Componente de notificação toast para exibir mensagens de sucesso e erro de forma flutuante na tela.

### Características

- **Duas variações**: Success (verde) e Error (vermelho)
- **Posicionamento flutuante**: Aparece na área inferior da tela
- **Auto-dismiss**: Remove automaticamente após duração configurável
- **Botão de fechar**: Permite fechamento manual
- **Sombras elevadas**: Visual moderno com múltiplas sombras
- **Animações**: Entrada e saída suaves
- **Responsivo**: Largura máxima de 416dp com altura adaptativa

### Uso do Toast

#### Importação
```dart
import 'package:app/components/notifications/notifications.dart';
```

#### Toast de Sucesso
```dart
ZuzToastManager.showSuccess(
  context,
  message: 'Operação realizada com sucesso!',
  subtitle: 'Os dados foram salvos no sistema.',
);
```

#### Toast de Erro
```dart
ZuzToastManager.showError(
  context,
  message: 'Erro ao processar solicitação',
  subtitle: 'Verifique sua conexão e tente novamente.',
);
```

---

## ZuzInformation

Componente de informação para exibir mensagens contextuais inseridas diretamente no layout.

### Características

- **Quatro variações**: Positive, Warning, Error, Neutral
- **Inserido no layout**: Não é flutuante como o toast
- **Sem sombras**: Design limpo para integração no layout
- **Ícones específicos**: Cada tipo tem seu ícone característico
- **Cores semânticas**: Usa as cores do design system
- **Botão de fechar opcional**: Pode ser removido conforme necessário

### Variações do Information

#### Positive (Success)
- **Cor do texto**: `AppColors.successTexts` (#065F46)
- **Background**: `AppColors.successBoxes` (#ECFDF5)
- **Ícone**: Check com círculo (`Icons.check_circle`)
- **Cor do ícone**: `AppColors.success`

```dart
ZuzInformation.positive(
  message: 'Operação realizada com sucesso!',
  subtitle: 'Seus dados foram salvos no sistema.',
)
```

#### Warning
- **Cor do texto**: `AppColors.warningTexts` (#92400E)
- **Background**: `AppColors.warningBoxes` (#FFFBEB)
- **Ícone**: Exclamação em triângulo (`Icons.warning`)
- **Cor do ícone**: `AppColors.warning`

```dart
ZuzInformation.warning(
  message: 'Atenção: Prazo está se aproximando',
  subtitle: 'Você tem 3 dias para completar esta tarefa.',
)
```

#### Error
- **Cor do texto**: `AppColors.errorTexts` (#991B1B)
- **Background**: `AppColors.errorBoxes` (#FEF2F2)
- **Ícone**: X com círculo (`Icons.cancel`)
- **Cor do ícone**: `AppColors.error`

```dart
ZuzInformation.error(
  message: 'Erro ao processar solicitação',
  subtitle: 'Verifique os dados informados e tente novamente.',
)
```

#### Neutral (System Info)
- **Cor do texto**: `AppColors.systemInfoTexts` (#1E40AF)
- **Background**: `AppColors.systemInfoBoxes` (#EFF6FF)
- **Ícone**: Sem ícone
- **Uso**: Informações gerais do sistema

```dart
ZuzInformation.neutral(
  message: 'Nova atualização disponível',
  subtitle: 'Versão 2.1.0 com melhorias de performance.',
)
```

### Uso do Information

#### Construtor Genérico
```dart
ZuzInformation(
  type: ZuzInformationType.positive,
  message: 'Mensagem principal',
  subtitle: 'Informações extras opcionais',
  onClose: () {
    // Ação quando fechar
  },
  showCloseButton: true,
)
```

#### Sem Botão de Fechar
```dart
ZuzInformation.warning(
  message: 'Informação permanente',
  showCloseButton: false,
)
```

#### Com Callback Personalizado
```dart
ZuzInformation.error(
  message: 'Erro de conexão',
  subtitle: 'Clique no X para tentar novamente.',
  onClose: () {
    // Lógica de retry
    reconnect();
  },
)
```

---

## Quando Usar Cada Um

### Use ZuzToast quando:
- Confirmar ações do usuário (salvar, deletar, etc.)
- Mostrar feedback de operações assíncronas
- Exibir erros temporários (rede, servidor)
- Notificar sobre mudanças de estado do sistema

### Use ZuzInformation quando:
- Mostrar informações contextuais em formulários
- Exibir avisos em páginas específicas
- Indicar estados de validação inline
- Mostrar informações persistentes do sistema
- Guiar o usuário através de processos

---

## Estrutura dos Componentes

### Container Base (ambos)
- **Width**: Flexível (toast: max 416dp, information: full width)
- **Background**: Específico por tipo
- **Border Radius**: 8dp
- **Padding**: 16dp em todos os lados

### Layout
```
┌─────────────────────────────────────────────────────────┐
│ [Ícone*] Mensagem principal                   [Fechar] │
│          Informações extras                            │
└─────────────────────────────────────────────────────────┘
```
*Ícone presente em todos os tipos exceto Information.neutral

### Tipografia
- **Mensagem Principal**: 14sp, Regular, cor específica do tipo
- **Informações Extras**: 12sp, Regular, cor específica do tipo

### Animações (apenas Toast)
- **Entrada**: Slide up + fade in (300ms, easeOutBack)
- **Saída**: Slide down + fade out (300ms, easeOut)

---

## Integração com o Design System

Ambos os componentes utilizam as cores e tipografia definidas no `AppColors` e seguem as diretrizes do design system:

- **Cores semânticas**: Success, Warning, Error, System Info
- **Tipografia**: Century Gothic (fonte global do tema)
- **Espaçamentos**: Múltiplos de 4dp
- **Border radius**: 8dp (padrão do sistema)

Veja `lib/examples/toast_example.dart` e `lib/examples/information_example.dart` para exemplos práticos de uso.
