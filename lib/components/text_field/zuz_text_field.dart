import 'package:flutter/material.dart';
import '../../locator/locator.dart';
import '../../localization/repositories/locale_repository.dart';
import '../../theme/theme.dart';
import 'zuz_text_field_base.dart';

/// Campo de texto do aplicativo seguindo o Design System
/// Funciona tanto standalone quanto integrado com Form/validação automática
class ZuzTextField extends StatefulWidget {
  /// Label do campo
  final String label;
  
  /// Se o campo é opcional (mostra texto "Opcional")
  final bool isOptional;
  
  /// Controlador do campo de texto
  final TextEditingController? controller;
  
  /// Placeholder/hint do campo
  final String? placeholder;
  
  /// Texto de ajuda (helper text)
  final String? helperText;
  
  /// Limite máximo de caracteres
  final int? maxLength;
  
  /// Mensagem de erro personalizada (para uso standalone)
  final String? errorText;
  
  /// Se o campo está habilitado
  final bool enabled;
  
  /// Callback quando o texto muda
  final ValueChanged<String>? onChanged;
  
  /// Callback quando o campo perde o foco
  final VoidCallback? onEditingComplete;
  
  /// Tipo de teclado
  final TextInputType? keyboardType;
  
  /// Se o texto deve ser obscurecido (para senhas)
  final bool obscureText;
  
  /// Se é um campo de senha (adiciona automaticamente o botão de mostrar/ocultar)
  final bool isPassword;
  
  /// Se o campo deve permitir múltiplas linhas
  final bool isMultiline;
  
  /// Validador do campo (para uso com Form)
  final String? Function(String?)? validator;
  
  /// Valor inicial do campo
  final String? initialValue;
  
  /// Ícone à esquerda (opcional)
  final IconData? prefixIcon;
  
  /// Ícone à direita (opcional) - ex: olho para senha, lupa para busca
  final IconData? suffixIcon;
  
  /// Callback para quando o ícone à direita é pressionado
  final VoidCallback? onSuffixIconPressed;

  const ZuzTextField({
    super.key,
    required this.label,
    this.isOptional = false,
    this.controller,
    this.placeholder,
    this.helperText,
    this.maxLength,
    this.errorText,
    this.enabled = true,
    this.onChanged,
    this.onEditingComplete,
    this.keyboardType,
    this.obscureText = false,
    this.isPassword = false,
    this.isMultiline = false,
    this.validator,
    this.initialValue,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconPressed,
  });

  @override
  State<ZuzTextField> createState() => _ZuzTextFieldState();
}

class _ZuzTextFieldState extends State<ZuzTextField> {
  late TextEditingController _controller;
  int _currentLength = 0;
  String? _errorText;
  bool _isFocused = false;
  late FocusNode _focusNode;
  bool _isPasswordVisible = false;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController(text: widget.initialValue);
    _currentLength = _controller.text.length;
    _controller.addListener(_updateLength);
    
    _focusNode = FocusNode();
    _focusNode.addListener(_updateFocus);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    } else {
      _controller.removeListener(_updateLength);
    }
    _focusNode.dispose();
    super.dispose();
  }

  void _updateLength() {
    if (mounted) {
      setState(() {
        _currentLength = _controller.text.length;
      });
    }
  }

  void _updateFocus() {
    if (mounted) {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });
    }
  }

  void _togglePasswordVisibility() {
    setState(() {
      _isPasswordVisible = !_isPasswordVisible;
    });
  }

  @override
  Widget build(BuildContext context) {
    // Se tem validator, usa FormField. Senão, usa diretamente o TextField
    if (widget.validator != null) {
      return _buildWithFormField();
    } else {
      return _buildStandalone();
    }
  }

  /// Constrói o campo integrado com Form (quando tem validator)
  Widget _buildWithFormField() {
    return FormField<String>(
      initialValue: widget.initialValue,
      validator: widget.validator,
      builder: (FormFieldState<String> field) {
        // Atualiza o erro quando o campo é validado
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted && _errorText != field.errorText) {
            setState(() {
              _errorText = field.errorText;
            });
          }
        });

        return _buildTextField(
          errorText: _errorText,
          onChanged: (value) {
            field.didChange(value);
            widget.onChanged?.call(value);
          },
        );
      },
    );
  }

  /// Constrói o campo standalone (quando não tem validator)
  Widget _buildStandalone() {
    return _buildTextField(
      errorText: widget.errorText,
      onChanged: widget.onChanged,
    );
  }

  /// Constrói o TextField base usado em ambos os casos
  Widget _buildTextField({
    String? errorText,
    ValueChanged<String>? onChanged,
  }) {
    final hasError = errorText != null;
    
    // Determina se deve obscurecer o texto
    final bool shouldObscureText = widget.isPassword ? !_isPasswordVisible : widget.obscureText;
    
    // Determina o ícone à direita
    IconData? effectiveSuffixIcon;
    VoidCallback? effectiveSuffixIconPressed;
    
    if (widget.isPassword) {
      // Para campos de senha, usa automaticamente o ícone de mostrar/ocultar
      effectiveSuffixIcon = _isPasswordVisible ? Icons.visibility_off : Icons.visibility;
      effectiveSuffixIconPressed = _togglePasswordVisibility;
    } else {
      // Para outros campos, usa os valores fornecidos
      effectiveSuffixIcon = widget.suffixIcon;
      effectiveSuffixIconPressed = widget.onSuffixIconPressed;
    }
    
    return ZuzTextFieldBase(
      label: widget.label,
      isOptional: widget.isOptional,
      helperText: widget.helperText,
      characterCounter: widget.maxLength != null 
          ? '$_currentLength/${widget.maxLength}' 
          : null,
      errorText: errorText,
      optionalText: Locator.instance.get<LocaleRepository>().strings.componentsStrings.optional,
      child: Container(
        height: widget.isMultiline ? 143 : 39,
        decoration: BoxDecoration(
          color: widget.enabled ? AppColors.white : AppColors.gray1,
          border: Border.all(
            color: hasError 
                ? AppColors.error 
                : (_isFocused ? AppColors.primary : AppColors.gray3),
            width: _isFocused ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          crossAxisAlignment: widget.isMultiline ? CrossAxisAlignment.start : CrossAxisAlignment.center, // Alinha no topo para multilinha
          children: [
            // Ícone à esquerda (opcional)
            if (widget.prefixIcon != null) ...[
              Padding(
                padding: const EdgeInsets.only(left: 16),
                child: Icon(
                  widget.prefixIcon,
                  size: 20,
                  color: widget.enabled ? AppColors.gray5 : AppColors.gray4,
                ),
              ),
              const SizedBox(width: 8), // Gap de 8px
            ] else
              const SizedBox(width: 16), // Padding inicial se não tem ícone
            
            // Campo de texto expandido
            Expanded(
              child: TextField(
                controller: _controller,
                focusNode: _focusNode,
                enabled: widget.enabled,
                maxLines: widget.isMultiline ? null : 1,
                maxLength: widget.maxLength,
                keyboardType: widget.keyboardType,
                obscureText: shouldObscureText,
                onChanged: onChanged,
                onEditingComplete: widget.onEditingComplete,
                textAlignVertical: widget.isMultiline ? TextAlignVertical.top : TextAlignVertical.center, // Centraliza o texto verticalmente
                decoration: InputDecoration(
                  hintText: widget.placeholder,
                  counterText: '', // Remove o contador padrão
                  border: InputBorder.none, // Remove bordas do TextField
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  disabledBorder: InputBorder.none,
                  contentPadding: widget.isMultiline 
                      ? const EdgeInsets.symmetric(vertical: 12) 
                      : EdgeInsets.zero, // Adiciona padding vertical para multilinha
                  isDense: true, // Torna o campo mais compacto
                  hintStyle: const TextStyle(
                    color: AppColors.gray5,
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                style: const TextStyle(
                  color: AppColors.black,
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
            
            // Ícone à direita (opcional ou automático para senha)
            if (effectiveSuffixIcon != null) ...[
              const SizedBox(width: 8), // Gap de 8px
              Padding(
                padding: const EdgeInsets.only(right: 16),
                child: GestureDetector(
                  onTap: effectiveSuffixIconPressed,
                  child: Icon(
                    effectiveSuffixIcon,
                    size: 20,
                    color: hasError 
                        ? AppColors.error 
                        : (widget.enabled ? AppColors.gray5 : AppColors.gray4),
                  ),
                ),
              ),
            ] else
              const SizedBox(width: 16), // Padding final se não tem ícone
          ],
        ),
      ),
    );
  }
}
