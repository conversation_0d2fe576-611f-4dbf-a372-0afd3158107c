# ZuzTextField - Componente de Campo de Texto

Este diretório contém os componentes de campo de texto do aplicativo, seguindo rigorosamente o Design System especificado.

## Estrutura

### `ZuzTextFieldBase`
Componente base que define a estrutura padrão para todos os campos de texto. Contém:

- **Container principal**: `Column` com espaçamento de 4dp entre elementos
- **Header Row**: Label obrigatório + texto "Opcional" (se aplicável)
- **Campo filho**: Widget personalizado (input, dropdown, multiline, etc.)
- **Elementos opcionais** (exibidos conforme necessário):
  - Helper text
  - Contador de caracteres
  - Mensagem de erro

### `ZuzTextField`
Componente inteligente que funciona tanto standalone quanto integrado com `Form`. Automaticamente detecta se deve usar validação manual (`errorText`) ou integrada (`validator`).

## Uso - <PERSON><PERSON>

### 1. Uso Standalone (sem Form)
```dart
ZuzTextField(
  label: 'Nome',
  placeholder: 'Digite seu nome',
  errorText: 'Campo obrigatório', // Validação manual
  onChanged: (value) => print(value),
)
```

### 2. Uso com Form (validação automática)
```dart
Form(
  child: Column(
    children: [
      ZuzTextField(
        label: 'Email',
        placeholder: '<EMAIL>',
        validator: (value) => validateEmail(value), // Validação automática
        keyboardType: TextInputType.emailAddress,
      ),
    ],
  ),
)
```

## Detecção Automática

O componente detecta automaticamente qual modalidade usar:
- **Se `validator` for fornecido**: Integra com `Form` usando `FormField`
- **Se `validator` for null**: Funciona standalone usando `errorText`

## Exemplos Completos

```dart
// Campo obrigatório com validação
ZuzTextField(
  label: 'Nome completo',
  controller: nameController,
  placeholder: 'Digite seu nome completo',
  maxLength: 100,
  validator: (value) {
    if (value == null || value.isEmpty) {
      return 'O nome é obrigatório';
    }
    return null;
  },
  keyboardType: TextInputType.name,
)

// Campo opcional com helper text
ZuzTextField(
  label: 'Apelido',
  isOptional: true,
  placeholder: 'Como gostaria de ser chamado?',
  helperText: 'Este nome aparecerá no seu perfil público',
  maxLength: 30,
)

// Campo de senha
ZuzTextField(
  label: 'Senha',
  placeholder: 'Digite sua senha',
  obscureText: true,
  maxLength: 50,
  helperText: 'Mínimo de 8 caracteres',
  validator: (value) => validatePassword(value),
)

// Campo com erro manual (standalone)
ZuzTextField(
  label: 'Código',
  placeholder: 'Digite o código',
  errorText: hasError ? 'Código inválido' : null,
  onChanged: (value) => validateCode(value),
)
```

## Características do Design System

### Tipografia
- **Label**: 14sp, Century Gothic Bold, cor #111827
- **Opcional**: 14sp, Century Gothic Regular, cor #6B7280
- **Helper Text**: 12sp, Century Gothic Regular, cor #6B7280
- **Contador**: 14sp, Century Gothic Regular, cor #6B7280, alinhado à direita
- **Erro**: 14sp, Century Gothic Regular, cor #DC2626

### Espaçamentos
- Entre elementos principais: 4dp
- Padding interno do campo: 16dp horizontal, 16dp vertical
- Border radius: 8dp

### Estados do Campo
- **Normal**: Border #E5E7EB (1px)
- **Foco**: Border #78942B (2px)
- **Erro**: Border #DC2626 (1px no normal, 2px no foco)
- **Desabilitado**: Border #D1D5DB (1px), background #F9FAFB

## Criando Novos Tipos de Campo

Para criar variações (multiline, dropdown, etc.), use o `ZuzTextFieldBase`:

```dart
class ZuzMultilineField extends StatelessWidget {
  // ... propriedades

  @override
  Widget build(BuildContext context) {
    return ZuzTextFieldBase(
      label: label,
      isOptional: isOptional,
      helperText: helperText,
      characterCounter: maxLength != null ? '$currentLength/$maxLength' : null,
      errorText: errorText,
      child: TextField(
        maxLines: null, // Multiline
        // ... outras configurações
      ),
    );
  }
}
```

## Exemplos

Veja `lib/examples/text_field_example.dart` para exemplos completos de uso com validações e diferentes configurações.
