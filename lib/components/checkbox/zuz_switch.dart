import 'package:flutter/material.dart';
import '../../theme/theme.dart';

/// Switch customizado seguindo o Design System
/// 
/// Widget que combina um Switch com texto ao lado.
/// - Estado OFF: Cor Gray4 com ícone X
/// - Estado ON: Cor Success com ícone Check
/// - Texto ao lado com espaçamento de 8px
class ZuzSwitch extends StatelessWidget {
  /// Texto exibido ao lado do switch
  final String text;
  
  /// Valor atual do switch (true = ON, false = OFF)
  final bool value;
  
  /// Callback chamado quando o valor muda
  final ValueChanged<bool>? onChanged;

  const ZuzSwitch({
    super.key,
    required this.text,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onChanged != null ? () => onChanged!(!value) : null,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Switch customizado
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppColors.white, // Thumb branco quando ON
            inactiveThumbColor: AppColors.white, // Thumb branco quando OFF
            inactiveTrackColor: AppColors.gray4, // Track cinza quando OFF
            activeTrackColor: AppColors.success, // Track verde quando ON
            trackOutlineColor: WidgetStateProperty.resolveWith<Color>((states) {
              if (states.contains(WidgetState.selected)) {
                return AppColors.success; // Borda verde quando ON
              }
              return AppColors.gray4; // Borda cinza quando OFF (mesma cor do fundo)
            }),
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            thumbIcon: WidgetStateProperty.resolveWith<Icon?>((states) {
              if (states.contains(WidgetState.selected)) {
                // Ícone de check quando ON (cor do ícone success para contraste)
                return const Icon(
                  Icons.check,
                  color: AppColors.success,
                  size: 16,
                );
              } else {
                // Ícone de X quando OFF (cor do ícone gray4 para contraste)
                return const Icon(
                  Icons.close,
                  color: AppColors.gray6,
                  size: 16,
                );
              }
            }),
          ),
          
          const SizedBox(width: 8), // Espaçamento de 8px
          
          // Texto ao lado
          Text(
            text,
            style: const TextStyle(
              color: AppColors.black, // Neutral black text
              fontSize: 14,
              fontStyle: FontStyle.normal,
              fontWeight: FontWeight.w400,
              height: 21 / 14, // line-height: 21px (150%)
            ),
          ),
        ],
      ),
    );
  }
}
