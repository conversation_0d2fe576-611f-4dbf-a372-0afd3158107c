import 'package:flutter/material.dart';
import '../../theme/theme.dart';

/// Checkbox customizado seguindo o Design System
/// 
/// Widget simples que herda do Checkbox padrão do Material
/// com alterações específicas de estilo:
/// - Tamanho: 18x18px
/// - Padding: 3px
/// - Border radius: 4px
/// - Cores customizadas para diferentes estados
class ZuzCheckBox extends StatelessWidget {
  /// Valor atual do checkbox (true, false ou null para indeterminate)
  final bool? value;
  
  /// Callback chamado quando o valor muda
  final ValueChanged<bool?>? onChanged;
  
  /// Se está no estado tristate (permite valor null)
  final bool tristate;

  const ZuzCheckBox({
    super.key,
    required this.value,
    required this.onChanged,
    this.tristate = false,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 18,
      height: 18,
      child: Transform.scale(
        scale: 0.8, // Reduz o tamanho para adequar aos 18px
        child: Checkbox(
          value: value,
          onChanged: onChanged,
          tristate: tristate,
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          visualDensity: VisualDensity.compact,
          side: BorderSide(
            color: _getBorderColor(),
            width: 1,
          ),
          fillColor: WidgetStateProperty.resolveWith<Color>((states) {
            if (value == null) {
              return AppColors.gray6; // Cinza quando indeterminate
            }
            if (states.contains(WidgetState.selected)) {
              return AppColors.success; // Verde quando selecionado
            }
            return AppColors.white; // Branco quando desmarcado
          }),
          checkColor: AppColors.white, // Ícone branco
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ),
    );
  }
  
  /// Retorna a cor da borda baseada no estado atual
  Color _getBorderColor() {
    if (value == true) {
      return AppColors.success; // Verde quando selecionado
    }
    if (value == null) {
      return AppColors.gray6; // Cinza quando indeterminate
    }
    return AppColors.gray6; // Cinza padrão quando desmarcado
  }
}
