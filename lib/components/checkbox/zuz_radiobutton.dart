import 'package:flutter/material.dart';
import '../../theme/theme.dart';

/// Radio button customizado seguindo o Design System
/// 
/// Widget customizado que implementa um radio button com:
/// - Tamanho: 18x18px
/// - Borda: 1px (desmarcado) / 5px (selecionado)
/// - Cores: Gray6 (desmarcado) / Brand/Primary (selecionado)
/// - Transição suave entre estados
class ZuzRadioButton<T> extends StatelessWidget {
  /// Valor atual do radio button
  final T value;
  
  /// Valor do grupo (qual opção está selecionada)
  final T? groupValue;
  
  /// Callback chamado quando o valor muda
  final ValueChanged<T?>? onChanged;

  const ZuzRadioButton({
    super.key,
    required this.value,
    required this.groupValue,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final bool isSelected = value == groupValue;
    
    return GestureDetector(
      onTap: onChanged != null ? () => onChanged!(value) : null,
      child: SizedBox(
        width: 18,
        height: 18,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200), // Transição suave
          curve: Curves.easeInOut,
          width: 18,
          height: 18,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: AppColors.white, // Background sempre branco
            border: Border.all(
              color: isSelected ? AppColors.primary : AppColors.gray6,
              width: isSelected ? 5.0 : 1.0, // 5px quando selecionado, 1px quando não
            ),
          ),
        ),
      ),
    );
  }
}
