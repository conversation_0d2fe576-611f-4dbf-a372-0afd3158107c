import 'package:flutter/material.dart';
import '../../locator/locator.dart';
import '../../localization/repositories/locale_repository.dart';
import '../../theme/theme.dart';
import '../checkbox/zuz_checkbox.dart';
import '../text_field/zuz_text_field_base.dart';

/// Opção do dropdown
class ZuzDropdownOption<T> {
  final T value;
  final String label;
  
  const ZuzDropdownOption({
    required this.value,
    required this.label,
  });
  
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ZuzDropdownOption &&
          runtimeType == other.runtimeType &&
          value == other.value;

  @override
  int get hashCode => value.hashCode;
}

/// Dropdown do aplicativo seguindo o Design System
/// Funciona tanto para seleção única quanto múltipla
class ZuzDropdown<T> extends StatefulWidget {
  /// Label do campo
  final String label;
  
  /// Se o campo é opcional (mostra texto "Opcional")
  final bool isOptional;
  
  /// Lista de opções disponíveis
  final List<ZuzDropdownOption<T>> options;
  
  /// Placeholder/hint do campo quando nada está selecionado
  final String? placeholder;
  
  /// Texto de ajuda (helper text)
  final String? helperText;
  
  /// Mensagem de erro personalizada
  final String? errorText;
  
  /// Se o campo está habilitado
  final bool enabled;
  
  /// Se permite múltipla seleção
  final bool isMultiSelect;
  
  /// Valor selecionado (para seleção única)
  final T? selectedValue;
  
  /// Valores selecionados (para múltipla seleção)
  final List<T>? selectedValues;
  
  /// Callback quando o valor muda (seleção única)
  final ValueChanged<T?>? onChanged;
  
  /// Callback quando os valores mudam (múltipla seleção)
  final ValueChanged<List<T>>? onMultipleChanged;
  
  /// Validador do campo (para uso com Form)
  final String? Function(dynamic)? validator;
  
  /// Ícone à esquerda (opcional)
  final IconData? prefixIcon;
  
  /// Ícone à direita (opcional) - além do ícone padrão do dropdown
  final IconData? suffixIcon;
  
  /// Callback para quando o ícone à direita é pressionado
  final VoidCallback? onSuffixIconPressed;

  const ZuzDropdown({
    super.key,
    required this.label,
    required this.options,
    this.isOptional = false,
    this.placeholder,
    this.helperText,
    this.errorText,
    this.enabled = true,
    this.isMultiSelect = false,
    this.selectedValue,
    this.selectedValues,
    this.onChanged,
    this.onMultipleChanged,
    this.validator,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconPressed,
  }) : assert(
          !isMultiSelect || (onMultipleChanged != null),
          'Para múltipla seleção, forneça onMultipleChanged.',
        ),
        assert(
          isMultiSelect || (onChanged != null),
          'Para seleção única, forneça onChanged.',
        );

  @override
  State<ZuzDropdown<T>> createState() => _ZuzDropdownState<T>();
}

class _ZuzDropdownState<T> extends State<ZuzDropdown<T>> {
  String? _errorText;
  bool _isFocused = false;
  late FocusNode _focusNode;
  OverlayEntry? _overlayEntry;
  final GlobalKey _dropdownKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _focusNode.addListener(_updateFocus);
  }

  @override
  void dispose() {
    _removeOverlay();
    _focusNode.dispose();
    super.dispose();
  }

  void _updateFocus() {
    if (mounted) {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });
      
      if (_isFocused) {
        _showDropdown();
      } else {
        _removeOverlay();
      }
    }
  }

  void _showDropdown() {
    if (_overlayEntry != null) return;
    
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _updateOverlay() {
    if (_overlayEntry != null) {
      _removeOverlay();
      _showDropdown();
    }
  }

  OverlayEntry _createOverlayEntry() {
    final RenderBox renderBox = _dropdownKey.currentContext!.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final position = renderBox.localToGlobal(Offset.zero);

    return OverlayEntry(
      builder: (context) => GestureDetector(
        onTap: () {
          // Fecha o dropdown ao clicar fora
          _focusNode.unfocus();
        },
        child: Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          color: Colors.transparent,
          child: Stack(
            children: [
              Positioned(
                left: position.dx,
                top: position.dy + size.height + 4,
                width: size.width,
                child: GestureDetector(
                  onTap: () {
                    // Impede que o clique na lista feche o dropdown
                  },
                  child: Material(
                    elevation: 4,
                    borderRadius: BorderRadius.circular(4),
                    child: Container(
                      constraints: const BoxConstraints(maxHeight: 200),
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: AppColors.gray3),
                      ),
                      child: ListView.builder(
                        padding: EdgeInsets.zero,
                        shrinkWrap: true,
                        itemCount: widget.options.length,
                        itemBuilder: (context, index) {
                          final option = widget.options[index];
                          final isSelected = widget.isMultiSelect
                              ? widget.selectedValues?.contains(option.value) ?? false
                              : widget.selectedValue == option.value;

                          return _buildDropdownItem(option, isSelected);
                        },
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDropdownItem(ZuzDropdownOption<T> option, bool isSelected) {
    return InkWell(
      onTap: () => _onItemTap(option),
      child: Container(
        height: 40,
        padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
        child: Row(
          children: [
            Expanded(
              child: Text(
                option.label,
                style: TextStyle(
                  fontSize: 14,
                  height: 21 / 14,
                  fontWeight: isSelected ? FontWeight.w700 : FontWeight.w400,
                  color: AppColors.black,
                ),
              ),
            ),
            const SizedBox(width: 8),
            if (widget.isMultiSelect)
              ZuzCheckBox(
                value: isSelected,
                onChanged: (_) => _onItemTap(option),
              )
            else if (isSelected)
              const Icon(
                Icons.check,
                size: 20,
                color: AppColors.primary,
              ),
          ],
        ),
      ),
    );
  }

  void _onItemTap(ZuzDropdownOption<T> option) {
    if (widget.isMultiSelect) {
      final currentValues = List<T>.from(widget.selectedValues ?? []);
      if (currentValues.contains(option.value)) {
        currentValues.remove(option.value);
      } else {
        currentValues.add(option.value);
      }
      widget.onMultipleChanged?.call(currentValues);
      // Para múltipla seleção, mantém o dropdown aberto e atualiza a lista
      _updateOverlay();
    } else {
      widget.onChanged?.call(option.value);
      // Para seleção única, fecha o dropdown
      _focusNode.unfocus();
    }
  }

  void _onContainerTap() {
    if (widget.enabled) {
      if (_focusNode.hasFocus) {
        _focusNode.unfocus();
      } else {
        _focusNode.requestFocus();
      }
    }
  }

  Widget _buildSelectedContent() {
    if (widget.isMultiSelect) {
      final selectedValues = widget.selectedValues ?? [];
      if (selectedValues.isEmpty) {
        return Text(
          widget.placeholder ?? '',
          style: const TextStyle(
            color: AppColors.gray5,
            fontSize: 16,
            fontWeight: FontWeight.w400,
          ),
        );
      }
      
      return Wrap(
        spacing: 8,
        runSpacing: 4,
        children: selectedValues.map((value) {
          final option = widget.options.firstWhere((opt) => opt.value == value);
          return _buildSelectedChip(option);
        }).toList(),
      );
    } else {
      if (widget.selectedValue == null) {
        return Text(
          widget.placeholder ?? '',
          style: const TextStyle(
            color: AppColors.gray5,
            fontSize: 16,
            fontWeight: FontWeight.w400,
          ),
        );
      }
      
      final selectedOption = widget.options.firstWhere(
        (opt) => opt.value == widget.selectedValue,
      );
      
      return Text(
        selectedOption.label,
        style: const TextStyle(
          color: AppColors.black,
          fontSize: 16,
          fontWeight: FontWeight.w400,
        ),
      );
    }
  }

  Widget _buildSelectedChip(ZuzDropdownOption<T> option) {
    return Container(
      height: 29,
      decoration: BoxDecoration(
        color: AppColors.gray0,
        borderRadius: BorderRadius.circular(4),
      ),
      padding: const EdgeInsets.fromLTRB(8, 4, 8, 4),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: Text(
              option.label,
              style: const TextStyle(
                fontSize: 14,
                height: 21 / 14,
                fontWeight: FontWeight.w400,
                color: AppColors.black,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 8),
          GestureDetector(
            onTap: () => _removeSelectedItem(option.value),
            child: Container(
              width: 16,
              height: 16,
              decoration: const BoxDecoration(
                color: AppColors.white,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.close,
                size: 12,
                color: AppColors.gray6,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _removeSelectedItem(T value) {
    if (widget.isMultiSelect) {
      final currentValues = List<T>.from(widget.selectedValues ?? []);
      currentValues.remove(value);
      widget.onMultipleChanged?.call(currentValues);
    }
  }

  @override
  Widget build(BuildContext context) {
    // Se tem validator, usa FormField. Senão, usa diretamente
    if (widget.validator != null) {
      return _buildWithFormField();
    } else {
      return _buildStandalone();
    }
  }

  /// Constrói o campo integrado com Form (quando tem validator)
  Widget _buildWithFormField() {
    final initialValue = widget.isMultiSelect 
        ? widget.selectedValues 
        : widget.selectedValue;
        
    return FormField<dynamic>(
      initialValue: initialValue,
      validator: widget.validator,
      builder: (FormFieldState<dynamic> field) {
        // Atualiza o erro quando o campo é validado
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted && _errorText != field.errorText) {
            setState(() {
              _errorText = field.errorText;
            });
          }
        });

        return _buildDropdown(
          errorText: _errorText,
          onChanged: (value) {
            field.didChange(value);
          },
        );
      },
    );
  }

  /// Constrói o campo standalone (quando não tem validator)
  Widget _buildStandalone() {
    return _buildDropdown(
      errorText: widget.errorText,
    );
  }

  /// Constrói o Dropdown base usado em ambos os casos
  Widget _buildDropdown({
    String? errorText,
    ValueChanged<dynamic>? onChanged,
  }) {
    final hasError = errorText != null;

    return ZuzTextFieldBase(
      label: widget.label,
      isOptional: widget.isOptional,
      helperText: widget.helperText,
      errorText: errorText,
      optionalText: Locator.instance.get<LocaleRepository>().strings.componentsStrings.optional,
      child: Container(
        key: _dropdownKey,
        height: widget.isMultiSelect ? null : 39,
        constraints: widget.isMultiSelect 
            ? const BoxConstraints(minHeight: 39)
            : null,
        decoration: BoxDecoration(
          color: widget.enabled ? AppColors.white : AppColors.gray1,
          border: Border.all(
            color: hasError 
                ? AppColors.error 
                : (_isFocused ? AppColors.primary : AppColors.gray3),
            width: _isFocused ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: GestureDetector(
          onTap: _onContainerTap,
          child: Focus(
            focusNode: _focusNode,
            child: Row(
              crossAxisAlignment: widget.isMultiSelect 
                  ? CrossAxisAlignment.start 
                  : CrossAxisAlignment.center,
              children: [
                // Ícone à esquerda (opcional)
                if (widget.prefixIcon != null) ...[
                  Padding(
                    padding: EdgeInsets.only(
                      left: 16,
                      top: widget.isMultiSelect ? 12 : 0,
                    ),
                    child: Icon(
                      widget.prefixIcon,
                      size: 20,
                      color: widget.enabled ? AppColors.gray5 : AppColors.gray4,
                    ),
                  ),
                  const SizedBox(width: 8),
                ] else
                  SizedBox(
                    width: 16,
                    height: widget.isMultiSelect ? 8 : 0,
                  ),
                
                // Conteúdo expandido
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      vertical: widget.isMultiSelect ? 8 : 0,
                    ),
                    child: _buildSelectedContent(),
                  ),
                ),
                
                // Ícone à direita personalizado (opcional)
                if (widget.suffixIcon != null) ...[
                  const SizedBox(width: 8),
                  Padding(
                    padding: EdgeInsets.only(
                      top: widget.isMultiSelect ? 12 : 0,
                    ),
                    child: GestureDetector(
                      onTap: widget.onSuffixIconPressed,
                      child: Icon(
                        widget.suffixIcon,
                        size: 20,
                        color: hasError 
                            ? AppColors.error 
                            : (widget.enabled ? AppColors.gray5 : AppColors.gray4),
                      ),
                    ),
                  ),
                ],
                
                // Ícone de dropdown (sempre presente)
                const SizedBox(width: 8),
                Padding(
                  padding: EdgeInsets.only(
                    right: 16,
                    top: widget.isMultiSelect ? 12 : 0,
                  ),
                  child: GestureDetector(
                    onTap: _onContainerTap,
                    child: Icon(
                      _isFocused ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                      size: 20,
                      color: widget.enabled ? AppColors.gray5 : AppColors.gray4,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
