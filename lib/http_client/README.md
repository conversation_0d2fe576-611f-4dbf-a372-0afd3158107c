# <PERSON><PERSON><PERSON><PERSON> `http_client`

Responsável pela comunicação HTTP da aplicação. Fornece uma interface (`HttpClientService`) e uma implementação baseada em `dio` (`HttpClientServiceImplementation`), além de modelos auxiliares para métodos, erros e mensagens localizadas.

## Princípios

- Interface primeiro: `HttpClientService` define um contrato simples para requisições.
- Implementação desacoplada: `HttpClientServiceImplementation` usa `dio` e pode ser trocada/mocada em testes.
- Mapeamento de erros: `DioException` é convertido para `HttpError` (que implementa `AppError`), permitindo i18n via `Strings`.
- Métodos explícitos: `HttpMethod` enum explicita `GET`, `POST`, `PUT`, `DELETE`.
- Mensagens i18n: `HttpStrings` define mensagens de erro e possui implementação `pt_BR`.

## Estrutura

- `models/http_method.dart`: enum de métodos HTTP.
- `models/http_error.dart`: erros HTTP mapeados e convertidos para mensagens via i18n.
- `models/http_strings.dart`: interface para mensagens de erro do domínio HTTP.
- `models/http_strings_pt_br.dart`: implementação pt_BR de `HttpStrings`.
- `services/http_client_service.dart`: contrato do cliente HTTP.
- `services/http_client_service_implementation.dart`: implementação com `dio`.
- `mock/mock_api_interceptor.dart`: interceptor de mock para desenvolvimento (simula respostas de endpoints sem rede).

## API (HttpClientService)

```dart
Future<dynamic> request({
  required String url,
  required HttpMethod method,
  Map<String, String>? headers,
  dynamic body,
});

void setAuthorizationHeader(String token);
void removeAuthorizationHeader();
```

## Mock de API (desenvolvimento)

Para facilitar o desenvolvimento sem depender do backend, este módulo oferece um interceptor de mock em `lib/http_client/mock/mock_api_interceptor.dart`.

- **Habilitar**: crie o cliente com `useMockApi: true`. O interceptor singleton `MockApiInterceptor.instance` será adicionado ao `Dio` dentro de `HttpClientServiceImplementation`.
- **Como funciona**: o interceptor procura uma rota mock que combine por método e caminho exatos, aplica um atraso artificial (1s) e retorna um `Response` simulado ou rejeita com `DioException`.

### Adicionando/alterando rotas mock

Edite a lista `_routes` em `lib/http_client/mock/mock_api_interceptor.dart` adicionando novas entradas do tipo `_MockRoute`:

```dart
final List<_MockRoute> _routes = [
  _MockRoute(
    method: HttpMethod.post,
    path: '/auth/signin',
    responder: (options) {
      // Exemplo simples de sucesso
      return Response<dynamic>(
        requestOptions: options,
        statusCode: 200,
        data: {'token': 'mock-token'},
      );
    },
  ),
  _MockRoute(
    method: HttpMethod.get,
    path: '/profile',
    responder: (options) => Response<dynamic>(
      requestOptions: options,
      statusCode: 200,
      data: {
        'name': 'Dev User',
        'email': '<EMAIL>',
      },
    ),
  ),
];
```

Regras atuais de matching:

- **Método**: enum `HttpMethod` (por exemplo, `HttpMethod.get`, `HttpMethod.post`).
- **Caminho**: comparação exata com `options.path` (ex.: `'/profile'`).
- **Atraso**: `Duration(seconds: 1)` para simular latência.

Importante:

- O mock é voltado a ambiente de desenvolvimento. Não habilite em produção.
- As respostas devem ser JSONs compatíveis com o consumo dos repositories/states.
- Para simular erro, lance um `DioException` com `Response<dynamic>` e o `statusCode` desejado.

## Erros e i18n

- `HttpError` implementa `AppError` e mapeia status codes para erros conhecidos (`badRequest`, `unauthorized`, etc.).
- `HttpError.messageFromStrings(Strings)` retorna a mensagem localizada via `Strings.httpStrings`.

## Exemplo de uso

```dart
final client = HttpClientServiceImplementation(baseUrl: 'https://api.exemplo.com');

final data = await client.request(
  url: '/usuarios',
  method: HttpMethod.get,
);
```

## Cabeçalho de autorização

- Use `setAuthorizationHeader('<token>')` imediatamente após o login/autenticação para que **todas as próximas requisições** enviem o token da sessão no cabeçalho `Authorization: Bearer <token>`.
- Quando o token não for mais necessário (ex.: logout, expiração ou troca de conta), chame `removeAuthorizationHeader()` para remover o cabeçalho e evitar uso indevido.

Exemplo:

```dart
final client = HttpClientServiceImplementation(baseUrl: 'https://api.exemplo.com');

// 1) Login na API e obtenção do token
final loginResponse = await client.post(
  '/auth/login',
  body: {
    'email': '<EMAIL>',
    'password': 'minha_senha_segura',
  },
);
final token = loginResponse['token'] as String;

// 2) Define o cabeçalho Authorization para as próximas requisições
client.setAuthorizationHeader(token);

// 3) Requisições autenticadas
final me = await client.get('/me');
final items = await client.get('/itens');

// 4) Em caso de logout/expiração/troca de conta
client.removeAuthorizationHeader();
```

## Boas práticas

- Defina `baseUrl` e timeouts via construtor.
- Centralize o token com `setAuthorizationHeader`/`removeAuthorizationHeader`.
- Trate `HttpError` nas camadas superiores (ex.: repositories) e converta para mensagens amigáveis com o módulo de `localization`.
