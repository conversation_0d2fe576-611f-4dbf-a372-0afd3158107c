import '../models/http_method.dart';

/// Contrato de cliente HTTP da aplicação.
///
/// Abstrai detalhes de transporte (ex.: `dio`) expondo um método genérico
/// `request` e auxiliares para cabeçalho de autorização.
abstract class HttpClientService {
  /// Executa uma requisição HTTP para a [url] usando o [method] informado.
  ///
  /// [headers] permite adicionar cabeçalhos.
  /// [body] será enviado como payload quando aplicável (POST/PUT/DELETE).
  Future<dynamic> request({
    required String url,
    required HttpMethod method,
    Map<String, String>? headers,
    dynamic body,
  });

  /// Define o cabeçalho `Authorization: Bearer <token>` nas requisições subsequentes.
  void setAuthorizationHeader(String token);

  /// Remove o cabeçalho de autorização previamente definido.
  void removeAuthorizationHeader();
}
