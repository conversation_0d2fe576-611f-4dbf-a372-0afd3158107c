import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../mock/mock_api_interceptor.dart';
import '../models/http_error.dart';
import '../models/http_method.dart';
import 'http_client_service.dart';

/// Implementação de `HttpClientService` baseada em `dio`.
///
/// - Configura `baseUrl`, timeouts e cabeçalhos padrão.
/// - Converte `DioException` em `HttpError` para tratamento uniforme e i18n.
/// - Fornece helpers `get/post/put/delete` além de `request` genérico.
/// - Suporte a mock de API em desenvolvimento via `useMockApi`.
///   - Quando `useMockApi` é `true`, o interceptor singleton
///     `MockApiInterceptor.instance` é registrado no `Dio` para retornar
///     respostas simuladas sem bater na rede (ver `mock/mock_api_interceptor.dart`).
class HttpClientServiceImplementation implements HttpClientService {
  HttpClientServiceImplementation({
    Dio? dio,
    String? baseUrl,
    Duration? connectTimeout,
    Duration? receiveTimeout,
    Duration? sendTimeout,
    bool useMockApi = false,
  }) : _dio = dio ?? Dio(),
       _useMockApi = useMockApi {
    _configureDio(
      baseUrl: baseUrl,
      connectTimeout: connectTimeout,
      receiveTimeout: receiveTimeout,
      sendTimeout: sendTimeout,
    );
  }

  final Dio _dio;
  final bool _useMockApi;

  /// Configura opções do `Dio` e adiciona interceptors (log + mock opcional).
  void _configureDio({
    String? baseUrl,
    Duration? connectTimeout,
    Duration? receiveTimeout,
    Duration? sendTimeout,
  }) {
    _dio.options = BaseOptions(
      baseUrl: baseUrl ?? '',
      connectTimeout: connectTimeout ?? const Duration(seconds: 30),
      receiveTimeout: receiveTimeout ?? const Duration(seconds: 30),
      sendTimeout: sendTimeout ?? const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );

    // Adiciona interceptors de log e tratamento de erros
    _dio.interceptors.add(
      LogInterceptor(
        requestBody: true,
        responseBody: true,
        error: true,
        logPrint: (object) => debugPrint(object.toString()),
      ),
    );

    // Mock de API no app (somente dev) — intercepta requisições e retorna respostas simuladas.
    if (_useMockApi) {
      _dio.interceptors.add(MockApiInterceptor.instance);
    }
  }

  @override
  /// Executa uma requisição HTTP genérica usando `Dio.request`.
  Future<dynamic> request({
    required String url,
    required HttpMethod method,
    Map<String, String>? headers,
    dynamic body,
  }) async {
    try {
      final options = Options(
        method: _getMethodString(method),
        headers: headers,
      );

      final response = await _dio.request(url, data: body, options: options);

      return response.data;
    } on DioException catch (error) {
      throw _handleDioError(error);
    } catch (_) {
      throw HttpError.unknown;
    }
  }

  /// Converte [method] para a string esperada pelo `Dio`.
  String _getMethodString(HttpMethod method) {
    return switch (method) {
      HttpMethod.get => 'GET',
      HttpMethod.post => 'POST',
      HttpMethod.put => 'PUT',
      HttpMethod.delete => 'DELETE',
    };
  }

  /// Mapeia erros do `Dio` para [HttpError].
  HttpError _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
      case DioExceptionType.connectionError:
        return HttpError.connection;
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        return HttpError.fromCode(statusCode);
      case DioExceptionType.cancel:
      case DioExceptionType.unknown:
      case DioExceptionType.badCertificate:
        return HttpError.unknown;
    }
  }

  /// Helper para requisições GET.
  Future<dynamic> get(String url, {Map<String, String>? headers}) {
    return request(url: url, method: HttpMethod.get, headers: headers);
  }

  /// Helper para requisições POST.
  Future<dynamic> post(
    String url, {
    dynamic body,
    Map<String, String>? headers,
  }) {
    return request(
      url: url,
      method: HttpMethod.post,
      body: body,
      headers: headers,
    );
  }

  /// Helper para requisições PUT.
  Future<dynamic> put(
    String url, {
    dynamic body,
    Map<String, String>? headers,
  }) {
    return request(
      url: url,
      method: HttpMethod.put,
      body: body,
      headers: headers,
    );
  }

  /// Helper para requisições DELETE.
  Future<dynamic> delete(String url, {Map<String, String>? headers}) {
    return request(url: url, method: HttpMethod.delete, headers: headers);
  }

  @override
  /// Define `Authorization: Bearer <token>` nas próximas requisições.
  void setAuthorizationHeader(String token) {
    _dio.options.headers['Authorization'] = 'Bearer $token';
  }

  @override
  /// Remove o cabeçalho de autorização previamente definido.
  void removeAuthorizationHeader() {
    _dio.options.headers.remove('Authorization');
  }
}
