import 'http_strings.dart';

/// Implementação `pt_BR` de mensagens do domínio HTTP.
class HttpStringsPtBr implements HttpStrings {
  @override
  String get connectionError => 'Erro de conexão';

  @override
  String get unknownError => 'Erro desconhecido';

  @override
  String get badRequestError => 'Requisição inválida';

  @override
  String get unauthorizedError => 'Não autorizado';

  @override
  String get forbiddenError => 'Acesso negado';

  @override
  String get notFoundError => 'Recurso não encontrado';

  @override
  String get serverError => 'Erro no servidor';
}
