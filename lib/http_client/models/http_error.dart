import 'package:zuz_app/localization/models/strings.dart';

import '../../error/models/app_error.dart';

/// Erros HTTP conhecidos, mapeados por status code e interoperáveis com i18n.
///
/// Implementa `AppError` para permitir conversão para mensagem localizada
/// via `Strings.httpStrings`.
enum HttpError implements AppError {
  badRequest(code: 400),
  unauthorized(code: 401),
  forbidden(code: 403),
  notFound(code: 404),
  server(code: 500),
  connection(),
  unknown();

  const HttpError({this.code});

  final int? code;

  /// Constrói um [HttpError] a partir de um status [code].
  /// Retorna [HttpError.unknown] quando o código não é reconhecido.
  static HttpError fromCode(int? code) {
    if (code == null) return unknown;
    final errorIndex = values.indexWhere((error) => error.code == code);
    if (errorIndex < 0) return unknown;
    return values[errorIndex];
  }

  @override
  /// Retorna a mensagem localizada correspondente ao erro atual usando [strings].
  String messageFromStrings(Strings strings) {
    return switch (this) {
      badRequest => strings.httpStrings.badRequestError,
      unauthorized => strings.httpStrings.unauthorizedError,
      forbidden => strings.httpStrings.forbiddenError,
      notFound => strings.httpStrings.notFoundError,
      server => strings.httpStrings.serverError,
      connection => strings.httpStrings.connectionError,
      unknown => strings.httpStrings.unknownError,
    };
  }
}
