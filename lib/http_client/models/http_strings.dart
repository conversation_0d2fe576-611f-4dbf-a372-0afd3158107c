/// Interface de mensagens i18n para o domínio HTTP.
abstract class HttpStrings {
  /// Erro de conectividade (timeout/falha de rede).
  String get connectionError;
  /// Erro desconhecido genérico.
  String get unknownError;
  /// 400: requisição inválida.
  String get badRequestError;
  /// 401: não autorizado.
  String get unauthorizedError;
  /// 403: acesso negado.
  String get forbiddenError;
  /// 404: recurso não encontrado.
  String get notFoundError;
  /// 500: erro no servidor.
  String get serverError;
}
