import 'dart:async';
import 'package:collection/collection.dart';
import 'package:dio/dio.dart';

import '../models/http_method.dart';

/// Interceptor de API mock para desenvolvimento.
///
/// Intercepta rotas selecionadas e retorna respostas simuladas
/// sem acessar a rede.
///
/// Padrão do projeto:
/// - Singleton: use `MockApiInterceptor.instance`.
/// - Matching de rotas por `HttpMethod` e `options.path` (comparação exata).
/// - Latência artificial de ~1s para simular rede.
/// - Respostas geradas por uma lista de rotas mock (`_routes`).
///
/// Como estender:
/// - Adicione novas entradas à lista `_routes` com `method`, `path` e `responder`.
/// - O `responder` deve retornar `Response<dynamic>` ou lançar `DioException` com
///   `Response<dynamic>` para simular erros.
class MockApiInterceptor extends Interceptor {
  MockApiInterceptor._();
  static final MockApiInterceptor _instance = MockApiInterceptor._();
  static MockApiInterceptor get instance => _instance;

  @override
  /// Intercepta a requisição antes do envio.
  ///
  /// - Tenta localizar uma rota mock em `_routes` cuja combinação [`method`, `path`]
  ///   seja idêntica à requisição.
  /// - Caso encontrada, aplica um atraso artificial e retorna um `Response` simulado.
  /// - Caso contrário, delega para o comportamento normal do `Dio`.
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    final method = options.method.toUpperCase();

    // Encontra a primeira rota correspondente
    final route = _routes.firstWhereOrNull(
      (route) =>
          route.method.name.toUpperCase() == method &&
          route.path == options.path,
    );

    if (route == null) {
      return super.onRequest(options, handler);
    }

    await Future<void>.delayed(const Duration(seconds: 1));
    try {
      final response = await route.responder(options);
      return handler.resolve(response);
    } on DioException catch (error) {
      return handler.reject(error);
    }
  }
}

/// Descreve uma rota mockável.
///
/// - `method`: método HTTP (enum `HttpMethod`).
/// - `path`: caminho exato a ser comparado com `RequestOptions.path`.
/// - `responder`: função que constrói o `Response` (ou lança `DioException`).
class _MockRoute {
  _MockRoute({
    required this.method,
    required this.path,
    required this.responder,
  });

  final HttpMethod method;
  final String path;
  final FutureOr<Response> Function(RequestOptions options) responder;
}

/// Rotas mock ativas.
///
/// Ajuste esta lista durante o desenvolvimento para simular diferentes
/// cenários de API (sucesso/erro, payloads variados, etc.).
final List<_MockRoute> _routes = [
  _MockRoute(
    method: HttpMethod.post,
    path: '/auth/signin',
    responder: (options) {
      final data = options.data is Map
          ? Map<String, dynamic>.from(options.data as Map)
          : <String, dynamic>{};
      final email = data['email'] as String?;
      final password = data['password'] as String?;

      if (email == '<EMAIL>' && password == '123456') {
        return Response<dynamic>(
          requestOptions: options,
          statusCode: 200,
          data: {'token': 'mock-token'},
        );
      }

      throw DioException(
        requestOptions: options,
        response: Response<dynamic>(
          requestOptions: options,
          statusCode: 400,
          data: {'error': 'invalid_credentials'},
        ),
        type: DioExceptionType.badResponse,
      );
    },
  ),
];
