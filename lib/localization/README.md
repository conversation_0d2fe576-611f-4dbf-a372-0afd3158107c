# Módulo `localization`

Centraliza a internacionalização (i18n) do app: locale atual, delegates, lista de idiomas suportados e acesso unificado às strings de cada domínio (ex.: `ErrorStrings`, `HttpStrings`, `LoginStrings`).

## Princípios de funcionamento

- Cada módulo/domínio define suas próprias interfaces de strings (ex.: `HttpStrings`, `LoginStrings`) e suas implementações por idioma (ex.: `HttpStringsPtBr`).
- O módulo `localization` expõe uma interface agregadora `Strings` que referencia essas interfaces de domínio.
- A implementação concreta de `Strings` para cada idioma (ex.: `StringsPtBr`) instancia as implementações específicas de cada domínio.
- Camadas de State/Builder acessam textos traduzidos via `LocaleRepository.strings`.

Isso garante:
- Separação de responsabilidades (cada módulo define suas próprias chaves de texto).
- Acesso unificado às strings via `Strings`, simplificando o consumo em States/Views.
- Facilidade para adicionar novos domínios de texto sem quebrar os existentes.

## Estrutura

- `models/strings.dart`: contrato agregador de strings por domínio (ex.: `errorStrings`, `httpStrings`, `loginStrings`).
- `models/strings_pt_br.dart`: implementação pt_BR do agregador `Strings`.
- `models/default_locale.dart`: locale padrão da aplicação.
- `repositories/locale_repository.dart`: interface p/ gerenciar locale atual, delegates e expor `Strings`.
- `repositories/locale_repository_implementation.dart`: implementação padrão do repositório de locale (carrega/salva locale e resolve `Strings`).

## Fluxo de uso

1. Em um State/Builder, obtenha `Strings` via `LocaleRepository.strings`.
2. Acesse a interface do domínio desejado (ex.: `strings.httpStrings`).
3. Use a chave de texto apropriada.

```dart
final Strings strings = localeRepository.strings;
final message = strings.httpStrings.timeout; // exemplo
```

## Como adicionar um novo domínio de strings

1. No módulo do domínio, crie a interface ex.: `payments/models/payments_strings.dart`.
2. Implemente por idioma, ex.: `payments_strings_pt_br.dart`.
3. Atualize `localization/models/strings.dart` adicionando o getter `paymentsStrings`.
4. Atualize `localization/models/strings_pt_br.dart` retornando `PaymentsStringsPtBr()`.
5. Utilize em States/Views via `localeRepository.strings.paymentsStrings`.

## Suporte a idiomas

- `LocaleRepositoryImplementation.supportedLocales` define os idiomas disponíveis.
- `LocaleRepositoryImplementation.strings` retorna a implementação de `Strings` correspondente ao `currentLocale`.
- `defaultLocale` em `models/default_locale.dart` define o idioma padrão.

## Boas práticas

- Evite textos literais em States/Views; sempre use `Strings`.
- Nomeie chaves de forma consistente entre idiomas.
- Centralize novas interfaces de domínio em seus respectivos módulos e integre no agregador `Strings`.
