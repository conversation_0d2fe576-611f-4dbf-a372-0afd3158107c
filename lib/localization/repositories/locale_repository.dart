import 'package:flutter/material.dart';

import '../models/strings.dart';

/// Repositório responsável por gerenciar o locale atual e expor `Strings`.
///
/// Também provê `supportedLocales` e `localizationsDelegates` para configuração
/// do `MaterialApp`/`WidgetsApp` e métodos para persistir/carregar o locale.
abstract class LocaleRepository extends Listenable {
  /// Lista de locales suportados pela aplicação.
  List<Locale> get supportedLocales;
  /// Delegates necessários para internacionalização do Flutter.
  List<LocalizationsDelegate> get localizationsDelegates;
  /// Locale atualmente em uso.
  Locale get currentLocale;
  /// Define o locale em uso e notifica ouvintes.
  Future<void> setLocale(Locale locale);
  /// Carrega o locale previamente salvo (se existir).
  Future<void> loadSavedLocale();
  /// Agregador de strings traduzidas por domínio.
  Strings get strings;
}
