import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import '../../local_storage/models/local_storage_key.dart';
import '../../local_storage/services/local_storage_service.dart';
import '../models/default_locale.dart';
import 'locale_repository.dart';
import '../models/strings.dart';
import '../models/strings_pt_br.dart';

/// Implementação padrão de `LocaleRepository`.
///
/// - Gerencia o `currentLocale` e persiste mudanças em `LocalStorageService`.
/// - Expõe `supportedLocales` e `localizationsDelegates` para configuração do app.
/// - Resolve o agregador `Strings` com base no idioma atual.
class LocaleRepositoryImplementation extends ChangeNotifier
    implements LocaleRepository {
  LocaleRepositoryImplementation({
    required LocalStorageService localStorageService,
    Locale initialLocale = defaultLocale,
  }) : _localStorageService = localStorageService,
       _currentLocale = initialLocale;

  final LocalStorageService _localStorageService;
  Locale _currentLocale;

  @override
  List<Locale> get supportedLocales => [const Locale('pt', 'BR')];

  @override
  List<LocalizationsDelegate> get localizationsDelegates => [
    GlobalMaterialLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
  ];

  @override
  Locale get currentLocale => _currentLocale;

  @override
  Future<void> setLocale(Locale locale) async {
    _currentLocale = locale;
    final localeString = _localeToString(locale);
    await _localStorageService.setValue(LocalStorageKey.locale, localeString);
    notifyListeners();
  }

  @override
  Future<void> loadSavedLocale() async {
    final localeString = await _localStorageService.getValue<String>(
      LocalStorageKey.locale,
    );
    if (localeString != null) {
      _currentLocale = _stringToLocale(localeString);
    }
  }

  String _localeToString(Locale locale) {
    var localeString = locale.languageCode;
    if ((locale.countryCode ?? '').isNotEmpty) {
      localeString += '_${locale.countryCode}';
    }
    return localeString;
  }

  Locale _stringToLocale(String localeString) {
    final parts = localeString.split('_');
    return Locale(
      parts[0],
      parts.length > 1 && parts[1].isNotEmpty ? parts[1] : null,
    );
  }

  @override
  Strings get strings {
    return switch (currentLocale.languageCode) {
      'pt' => StringsPtBr(),
      _ => StringsPtBr(),
    };
  }
}
