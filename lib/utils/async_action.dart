import 'package:flutter/foundation.dart';

import '../error/state/error_handler_state.dart';
import '../locator/locator.dart';

class AsyncAction extends ChangeNotifier {
  /// Gerencia uma ação assíncrona, permitindo executá-la em [execute], obter seu estado de execução com [isRunning] e mostrar uma mensagem em caso de erro.
  ///
  /// [action] é a ação assíncrona a ser executada. Se não for informada, a ação passada em [execute] será usada.
  ///
  /// [ignoreError] define se erros devem ser ignorados.
  AsyncAction({AsyncCallback? action, bool ignoreError = false})
    : _action = action,
      _ignoreError = ignoreError;

  final AsyncCallback? _action;
  bool _isRunning = false;
  bool get isRunning => _isRunning;

  final bool _ignoreError;
  ErrorHandlerState get _errorState => Locator.instance.get<ErrorHandlerState>();

  /// Executa a ação assíncrona, caso [isRunning] seja `false`.
  ///
  /// [action] é a ação assíncrona a ser executada. Se não for informada, a ação passada em [AsyncAction] será usada.
  Future<void> execute({AsyncCallback? action}) async {
    if (_action == null && action == null) return;
    action ??= _action;
    if (_isRunning) return;
    _isRunning = true;
    notifyListeners();
    try {
      await action?.call();
    } catch (error) {
      if (_ignoreError) return;
      _errorState.showMessage(error);
    } finally {
      _isRunning = false;
      notifyListeners();
    }
  }
}
