import 'package:flutter/material.dart';

import '../components/tab/zuz_tab.dart';

/// Exemplo de uso do ZuzTab
class ZuzTabExample extends StatefulWidget {
  const ZuzTabExample({super.key});

  @override
  State<ZuzTabExample> createState() => _ZuzTabExampleState();
}

class _ZuzTabExampleState extends State<ZuzTabExample> {
  ZuzTabBarType _currentType = ZuzTabBarType.underline;
  ZuzTabBarSizing _currentSizing = ZuzTabBarSizing.uniform;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Exemplo ZuzTabBar'),
      ),
      body: Column(
        children: [
          // Controles
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Tipo de Design:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                SegmentedButton<ZuzTabBarType>(
                  segments: const [
                    ButtonSegment(
                      value: ZuzTabBarType.underline,
                      label: Text('Underline'),
                    ),
                    ButtonSegment(
                      value: ZuzTabBarType.background,
                      label: Text('Background'),
                    ),
                  ],
                  selected: {_currentType},
                  onSelectionChanged: (Set<ZuzTabBarType> selection) {
                    setState(() {
                      _currentType = selection.first;
                    });
                  },
                ),
                const SizedBox(height: 16),
                const Text(
                  'Dimensionamento:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                SegmentedButton<ZuzTabBarSizing>(
                  segments: const [
                    ButtonSegment(
                      value: ZuzTabBarSizing.uniform,
                      label: Text('Uniform'),
                    ),
                    ButtonSegment(
                      value: ZuzTabBarSizing.adaptive,
                      label: Text('Adaptive'),
                    ),
                  ],
                  selected: {_currentSizing},
                  onSelectionChanged: (Set<ZuzTabBarSizing> selection) {
                    setState(() {
                      _currentSizing = selection.first;
                    });
                  },
                ),
              ],
            ),
          ),
          const Divider(),
          // Demonstração do ZuzTabBar
          Expanded(
            child: DefaultTabController(
              length: 4,
              child: Column(
                children: [
                  ZuzTabBar(
                    type: _currentType,
                    sizing: _currentSizing,
                    tabs: const [
                      Tab(text: 'Home'),
                      Tab(text: 'Search with long text'),
                      Tab(text: 'Profile'),
                      Tab(text: 'Settings'),
                    ],
                  ),
                  Expanded(
                    child: TabBarView(
                      children: [
                        _buildTabContent('Home'),
                        _buildTabContent('Search with long text'),
                        _buildTabContent('Profile'),
                        _buildTabContent('Settings'),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabContent(String title) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getIconForTab(title),
            size: 48,
            color: const Color(0xFF78942B),
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tipo: ${_currentType.name}',
            style: const TextStyle(color: Colors.grey),
          ),
          Text(
            'Sizing: ${_currentSizing.name}',
            style: const TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  IconData _getIconForTab(String title) {
    switch (title) {
      case 'Home':
        return Icons.home;
      case 'Search with long text':
        return Icons.search;
      case 'Profile':
        return Icons.person;
      case 'Settings':
        return Icons.settings;
      default:
        return Icons.tab;
    }
  }
}
