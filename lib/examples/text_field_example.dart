import 'package:flutter/material.dart';

import '../components/text_field/zuz_text_field.dart';

/// Exemplo de uso dos componentes ZuzTextField
class TextFieldExamplePage extends StatefulWidget {
  const TextFieldExamplePage({super.key});

  @override
  State<TextFieldExamplePage> createState() => _TextFieldExamplePageState();
}

class _TextFieldExamplePageState extends State<TextFieldExamplePage> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  
  String? _nameError;
  String? _emailError;

  void _validateName(String value) {
    setState(() {
      if (value.isEmpty) {
        _nameError = 'O nome é obrigatório';
      } else if (value.length < 2) {
        _nameError = 'O nome deve ter pelo menos 2 caracteres';
      } else {
        _nameError = null;
      }
    });
  }

  void _validateEmail(String value) {
    setState(() {
      if (value.isEmpty) {
        _emailError = 'O email é obrigatório';
      } else if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
        _emailError = 'Digite um email válido';
      } else {
        _emailError = null;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Exemplos de Text Field'),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
            // Campo obrigatório com validação
            ZuzTextField(
              label: 'Nome completo',
              controller: _nameController,
              placeholder: 'Digite seu nome completo',
              maxLength: 100,
              errorText: _nameError,
              onChanged: _validateName,
              keyboardType: TextInputType.name,
            ),
            
            const SizedBox(height: 24),
            
            // Campo opcional com helper text
            ZuzTextField(
              label: 'Apelido',
              isOptional: true,
              placeholder: 'Como gostaria de ser chamado?',
              helperText: 'Este nome aparecerá no seu perfil público',
              maxLength: 30,
            ),
            
            const SizedBox(height: 24),
            
            // Campo de email com ícone
            ZuzTextField(
              label: 'Email',
              controller: _emailController,
              placeholder: '<EMAIL>',
              prefixIcon: Icons.email_outlined,
              errorText: _emailError,
              onChanged: _validateEmail,
              keyboardType: TextInputType.emailAddress,
            ),
            
            const SizedBox(height: 24),
            
            // Campo de senha com botão de mostrar/ocultar automático
            ZuzTextField(
              label: 'Senha',
              placeholder: 'Digite sua senha',
              isPassword: true,
              maxLength: 50,
              helperText: 'Mínimo de 8 caracteres',
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'A senha é obrigatória';
                }
                if (value.length < 8) {
                  return 'A senha deve ter pelo menos 8 caracteres';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 24),
            
            // Campo de busca com ícone de lupa
            ZuzTextField(
              label: 'Buscar',
              isOptional: true,
              placeholder: 'Digite sua busca...',
              prefixIcon: Icons.search,
              suffixIcon: Icons.clear,
              onSuffixIconPressed: () {
                // Limpar campo de busca
              },
            ),
            
            const SizedBox(height: 24),
            
            // Campo desabilitado
            const ZuzTextField(
              label: 'Campo desabilitado',
              enabled: false,
              placeholder: 'Este campo está desabilitado',
              helperText: 'Você não pode editar este campo',
            ),
            
            const SizedBox(height: 24),
            
            // Campo multiline
            ZuzTextField(
              label: 'Comentários',
              isOptional: true,
              placeholder: 'Digite seus comentários ou observações...',
              isMultiline: true,
              maxLength: 500,
              helperText: 'Campo com múltiplas linhas e scroll automático',
              keyboardType: TextInputType.multiline,
            ),
            
            const SizedBox(height: 24),
            
            // Campo multiline obrigatório
            ZuzTextField(
              label: 'Descrição detalhada',
              placeholder: 'Descreva detalhadamente o problema encontrado...',
              isMultiline: true,
              maxLength: 1000,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'A descrição é obrigatória';
                }
                if (value.length < 20) {
                  return 'A descrição deve ter pelo menos 20 caracteres';
                }
                return null;
              },
            ),
          ],
          ),
        ),
      ), // Fecha SingleChildScrollView
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }
}
