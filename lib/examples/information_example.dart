import 'package:flutter/material.dart';

import '../components/button/button.dart';
import '../components/notifications/zuz_information.dart';
import '../components/text_field/zuz_text_field.dart';
import '../theme/theme.dart';

/// Página de exemplo demonstrando o uso dos information widgets
class InformationExamplePage extends StatelessWidget {
  const InformationExamplePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Information Examples'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'ZuzInformation Examples',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 32),
            
            // Information Positive
            const Text(
              'Positive (Success)',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ZuzInformation.positive(
              message: 'Operação realizada com sucesso!',
              subtitle: 'Seus dados foram salvos no sistema.',
            ),
            const SizedBox(height: 24),
            
            // Information Warning
            const Text(
              'Warning',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ZuzInformation.warning(
              message: 'Atenção: Prazo está se aproximando',
              subtitle: 'Você tem 3 dias para completar esta tarefa.',
            ),
            const SizedBox(height: 24),
            
            // Information Error
            const Text(
              'Error',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ZuzInformation.error(
              message: 'Erro ao processar solicitação',
              subtitle: 'Verifique os dados informados e tente novamente.',
            ),
            const SizedBox(height: 24),
            
            // Information Neutral
            const Text(
              'Neutral (System Info)',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ZuzInformation.neutral(
              message: 'Nova atualização disponível',
              subtitle: 'Versão 2.1.0 com melhorias de performance.',
            ),
            const SizedBox(height: 32),
            
            // Exemplos sem subtitle
            const Text(
              'Exemplos sem subtitle',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            ZuzInformation.positive(
              message: 'Arquivo carregado com sucesso!',
            ),
            const SizedBox(height: 16),
            
            ZuzInformation.warning(
              message: 'Memória do dispositivo está baixa',
            ),
            const SizedBox(height: 16),
            
            ZuzInformation.error(
              message: 'Falha na conexão com o servidor',
            ),
            const SizedBox(height: 16),
            
            ZuzInformation.neutral(
              message: 'Sistema será atualizado às 02:00',
            ),
            const SizedBox(height: 32),
            
            // Exemplos sem botão de fechar
            const Text(
              'Exemplos sem botão de fechar',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            ZuzInformation.positive(
              message: 'Configuração salva automaticamente',
              showCloseButton: false,
            ),
            const SizedBox(height: 16),
            
            ZuzInformation.neutral(
              message: 'Informação permanente do sistema',
              subtitle: 'Esta mensagem não pode ser fechada pelo usuário.',
              showCloseButton: false,
            ),
            const SizedBox(height: 32),
            
            // Exemplos com callback
            const Text(
              'Exemplos com ações',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            ZuzInformation.warning(
              message: 'Backup não realizado há 7 dias',
              subtitle: 'Clique no X para iniciar backup agora.',
              onClose: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Iniciando backup...'),
                  ),
                );
              },
            ),
            const SizedBox(height: 16),
            
            ZuzInformation.error(
              message: 'Erro de sincronização',
              subtitle: 'Clique no X para tentar novamente.',
              onClose: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Tentando sincronizar novamente...'),
                  ),
                );
              },
            ),
            const SizedBox(height: 32),
            
            // Exemplo de uso em formulários
            const Text(
              'Uso em formulários',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Simulando um formulário
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.gray3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  const ZuzTextField(
                    label: 'Email',
                    placeholder: 'Digite seu email',
                  ),
                  const SizedBox(height: 16),
                  
                  ZuzInformation.warning(
                    message: 'Este email já está cadastrado',
                    subtitle: 'Tente fazer login ou use outro email.',
                    showCloseButton: false,
                  ),
                  const SizedBox(height: 16),
                  
                  ZuzButton(
                    text: 'Continuar',
                    onPressed: () {},
                    width: double.infinity,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
