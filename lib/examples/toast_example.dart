import 'package:flutter/material.dart';

import '../components/button/button.dart';
import '../components/notifications/zuz_toast.dart';

/// Página de exemplo demonstrando o uso dos toasts
class ToastExamplePage extends StatelessWidget {
  const ToastExamplePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Toast Examples'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'ZuzToast Examples',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 32),
            
            // Toast de sucesso simples
            ZuzButton(
              text: 'Success Toast',
              onPressed: () {
                ZuzToastManager.showSuccess(
                  context,
                  message: 'Mensagem de sucesso',
                  subtitle: 'Informações extras sobre a operação',
                );
              },
            ),
            const SizedBox(height: 16),
            
            // Toast de erro simples
            ZuzButton(
              text: 'Error Toast',
              variant: ZuzButtonVariant.destructive,
              onPressed: () {
                ZuzToastManager.showError(
                  context,
                  message: 'Mensagem de fracasso',
                  subtitle: 'Detalhes sobre o erro ocorrido',
                );
              },
            ),
            const SizedBox(height: 16),
            
            // Toast de sucesso sem subtitle
            ZuzButton(
              text: 'Success (sem subtitle)',
              variant: ZuzButtonVariant.secondary,
              onPressed: () {
                ZuzToastManager.showSuccess(
                  context,
                  message: 'Operação realizada com sucesso!',
                );
              },
            ),
            const SizedBox(height: 16),
            
            // Toast de erro sem subtitle
            ZuzButton(
              text: 'Error (sem subtitle)',
              variant: ZuzButtonVariant.secondary,
              onPressed: () {
                ZuzToastManager.showError(
                  context,
                  message: 'Falha ao processar solicitação',
                );
              },
            ),
            const SizedBox(height: 16),
            
            // Toast com texto longo para testar overflow
            ZuzButton(
              text: 'Toast com texto longo',
              variant: ZuzButtonVariant.secondary,
              onPressed: () {
                ZuzToastManager.showSuccess(
                  context,
                  message: 'Esta é uma mensagem muito longa que pode causar overflow se não for tratada adequadamente no componente',
                  subtitle: 'Este é um subtitle também muito longo que deve ser tratado adequadamente para evitar problemas de layout e overflow. Aqui temos ainda mais texto para garantir que o componente consegue lidar com várias linhas sem problemas visuais.',
                );
              },
            ),
            const SizedBox(height: 16),
            
            // Toast com duração customizada
            ZuzButton(
              text: 'Toast (6 segundos)',
              variant: ZuzButtonVariant.tertiary,
              onPressed: () {
                ZuzToastManager.showSuccess(
                  context,
                  message: 'Toast com duração estendida',
                  subtitle: 'Este toast ficará visível por 6 segundos',
                  duration: const Duration(seconds: 6),
                );
              },
            ),
            const SizedBox(height: 16),
            
            // Toast com callback personalizado
            ZuzButton(
              text: 'Toast com callback',
              variant: ZuzButtonVariant.tertiary,
              onPressed: () {
                ZuzToastManager.showSuccess(
                  context,
                  message: 'Clique no X para ação customizada',
                  subtitle: 'Ou aguarde 4 segundos',
                  onClose: () {
                    // Ação personalizada quando fechar
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Toast foi fechado manualmente!'),
                      ),
                    );
                  },
                );
              },
            ),
            const SizedBox(height: 32),
            
            // Botão para limpar toasts
            ZuzButton(
              text: 'Limpar Toasts',
              variant: ZuzButtonVariant.icon,
              icon: Icons.clear,
              onPressed: () {
                ZuzToastManager.clear();
              },
            ),
            const SizedBox(height: 32),
            
            // Exemplos de uso real
            const Text(
              'Exemplos de Uso Real:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            ZuzButton(
              text: 'Simular Login Sucesso',
              onPressed: () {
                _simulateLoginSuccess(context);
              },
            ),
            const SizedBox(height: 16),
            
            ZuzButton(
              text: 'Simular Erro de Rede',
              variant: ZuzButtonVariant.destructive,
              onPressed: () {
                _simulateNetworkError(context);
              },
            ),
            const SizedBox(height: 16),
            
            ZuzButton(
              text: 'Simular Salvar Dados',
              variant: ZuzButtonVariant.secondary,
              onPressed: () {
                _simulateSaveData(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Simula um login bem-sucedido
  void _simulateLoginSuccess(BuildContext context) {
    // Simula delay de processamento
    Future.delayed(const Duration(milliseconds: 500), () {
      ZuzToastManager.showSuccess(
        context,
        message: 'Login realizado com sucesso',
        subtitle: 'Bem-vindo ao sistema!',
      );
    });
  }

  /// Simula um erro de rede
  void _simulateNetworkError(BuildContext context) {
    ZuzToastManager.showError(
      context,
      message: 'Falha na conexão com o servidor',
      subtitle: 'Verifique sua internet e tente novamente',
      duration: const Duration(seconds: 5),
    );
  }

  /// Simula salvamento de dados
  void _simulateSaveData(BuildContext context) {
    ZuzToastManager.showSuccess(
      context,
      message: 'Dados salvos com sucesso',
      subtitle: 'Suas alterações foram salvas automaticamente',
      onClose: () {
        print('Dados salvos - toast fechado');
      },
    );
  }
}
