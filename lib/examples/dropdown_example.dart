import 'package:flutter/material.dart';

import '../components/dropdown/zuz_dropdown.dart';

/// Exemplo de uso do ZuzDropdown
class ZuzDropdownExample extends StatefulWidget {
  const ZuzDropdownExample({super.key});

  @override
  State<ZuzDropdownExample> createState() => _ZuzDropdownExampleState();
}

class _ZuzDropdownExampleState extends State<ZuzDropdownExample> {
  // Dropdown de seleção única
  String? _selectedFruit;
  
  // Dropdown de múltipla seleção
  List<String> _selectedColors = [];
  
  final List<ZuzDropdownOption<String>> _fruitOptions = [
    const ZuzDropdownOption(value: 'apple', label: 'Maçã'),
    const ZuzDropdownOption(value: 'banana', label: 'Banana'),
    const ZuzDropdownOption(value: 'orange', label: 'Laranja'),
    const ZuzDropdownOption(value: 'grape', label: 'Uva'),
    const ZuzDropdownOption(value: 'strawberry', label: '<PERSON>go'),
  ];
  
  final List<ZuzDropdownOption<String>> _colorOptions = [
    const ZuzDropdownOption(value: 'red', label: 'Vermel<PERSON>'),
    const ZuzDropdownOption(value: 'blue', label: 'Azul'),
    const ZuzDropdownOption(value: 'green', label: 'Verde'),
    const ZuzDropdownOption(value: 'yellow', label: 'Amarelo'),
    const ZuzDropdownOption(value: 'purple', label: 'Roxo'),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Exemplo ZuzDropdown'),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Dropdown de seleção única
            ZuzDropdown<String>(
              label: 'Fruta favorita',
              placeholder: 'Selecione uma fruta',
              options: _fruitOptions,
              selectedValue: _selectedFruit,
              onChanged: (value) {
                setState(() {
                  _selectedFruit = value;
                });
              },
              prefixIcon: Icons.apple,
              helperText: 'Escolha sua fruta favorita',
            ),
            
            const SizedBox(height: 24),
            
            // Dropdown de múltipla seleção
            ZuzDropdown<String>(
              label: 'Cores preferidas',
              placeholder: 'Selecione uma ou mais cores',
              options: _colorOptions,
              isMultiSelect: true,
              selectedValues: _selectedColors,
              onMultipleChanged: (values) {
                setState(() {
                  _selectedColors = values;
                });
              },
              prefixIcon: Icons.palette,
              helperText: 'Você pode escolher várias cores',
            ),
            
            const SizedBox(height: 24),
            
            // Dropdown opcional
            ZuzDropdown<String>(
              label: 'Campo opcional',
              isOptional: true,
              placeholder: 'Selecione se desejar',
              options: _fruitOptions,
              selectedValue: null,
              onChanged: (value) {
                // Handle optional field change
              },
            ),
            
            const SizedBox(height: 24),
            
            // Dropdown desabilitado
            ZuzDropdown<String>(
              label: 'Campo desabilitado',
              placeholder: 'Este campo está desabilitado',
              options: _fruitOptions,
              selectedValue: 'apple',
              enabled: false,
              onChanged: (value) {
                // This won't be called
              },
            ),
            
            const SizedBox(height: 24),
            
            // Mostrar valores selecionados
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Valores selecionados:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('Fruta: ${_selectedFruit ?? "Nenhuma"}'),
                    const SizedBox(height: 4),
                    Text('Cores: ${_selectedColors.isEmpty ? "Nenhuma" : _selectedColors.join(", ")}'),
                  ],
                ),
              ),
            ),
          ],
        ),
        ),
      ),
    );
  }
}
