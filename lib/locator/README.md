# Módulo `locator`

Responsável pela injeção de dependências (DI) do app. Centraliza o registro e a resolução de serviços, repositórios e states usando um wrapper de `get_it` (`Locator`).

## Princípios

- Interface primeiro: `LocatorInterface` define as operações básicas de registro/recuperação.
- Encapsulamento do container: `Locator` encapsula `GetIt` e expõe uma API mínima e estável.
- Lazy singletons por padrão: criação apenas no primeiro uso, com suporte a `resetLazySingleton` para limpar instâncias quando necessário (ex.: ao sair de um fluxo com estado).
- Ponto único de configuração: `initializeDependencies()` registra todas as dependências da aplicação, preservando ordem e encapsulamento entre módulos.

## Estrutura

- `locator_interface.dart`: contrato do localizador (registrar/obter/checar/resetar).
- `locator.dart`: implementação singleton baseada em `GetIt`.
- `dependencies.dart`: função `initializeDependencies()` que registra todas as dependências do app.

## Uso

- Inicialização (em `main.dart`):

```dart
void main() {
  initializeDependencies();
  runApp(AppBuilder());
}
```

- Obter dependência em qualquer lugar do código:

```dart
final navigation = Locator.instance.get<NavigationRepository>();
```

- Registrar um lazy singleton (ex.: em `initializeDependencies`):

```dart
Locator.instance.registerLazySingleton<HttpClientService>(
  () => HttpClientServiceImplementation(),
);
```

- Resetar um lazy singleton (ex.: no `onExit` de uma rota para limpar estado):

```dart
Locator.instance.resetLazySingleton<EmailPasswordLoginState>();
```

## Boas práticas

- Prefira `registerLazySingleton` a `registerSingleton` para reduzir custo de inicialização.
- Use `resetLazySingleton` para estados escopados a fluxos (ex.: onboarding/login) quando sair da tela/rota.
- Mantenha `initializeDependencies()` como fonte única de registros; evite registros espalhados pelo código.
- Em testes, use `Locator.instance.reset()` para limpar registros entre casos.
