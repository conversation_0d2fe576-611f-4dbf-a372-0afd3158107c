/// Contrato do localizador de dependências (DI).
///
/// Define operações para registrar, obter e resetar instâncias.
abstract class LocatorInterface {
  /// Registra uma instância singleton imediatamente.
  void registerSingleton<T extends Object>(T instance);

  /// Registra um singleton lazy (criado apenas no primeiro uso).
  void registerLazySingleton<T extends Object>(T Function() factory);

  /// Reseta (descarta) o singleton lazy do tipo [T].
  void resetLazySingleton<T extends Object>();

  /// Obtém a instância registrada do tipo [T].
  T get<T extends Object>();

  /// Verifica se o tipo [T] está registrado.
  bool isRegistered<T extends Object>();
}
