import 'package:get_it/get_it.dart';

import 'locator_interface.dart';

/// Localizador de dependências (DI) baseado em `GetIt`.
///
/// - Permite registrar singletons imediatos e lazy singletons.
/// - Prefira `registerLazySingleton` para criar instâncias apenas no primeiro uso via [get].
/// - Use [resetLazySingleton] para descartar uma instância lazy e forçar recriação no próximo acesso (útil para limpar estados de fluxos).
class Locator implements LocatorInterface {
  Locator._();

  static final Locator _instance = Locator._();

  /// Obtém a instância singleton do `Locator`.
  static Locator get instance => _instance;

  /// Instância do `GetIt` utilizada internamente.
  final GetIt _getIt = GetIt.instance;

  @override
  /// Registra uma instância singleton imediata do tipo [T].
  void registerSingleton<T extends Object>(T instance) {
    _getIt.registerSingleton<T>(instance);
  }

  @override
  /// Registra um singleton lazy do tipo [T] com a [factory] fornecida.
  void registerLazySingleton<T extends Object>(T Function() factory) {
    _getIt.registerLazySingleton<T>(factory);
  }

  @override
  /// Reseta (descarta) o singleton lazy do tipo [T].
  void resetLazySingleton<T extends Object>() {
    _getIt.resetLazySingleton<T>();
  }

  @override
  /// Obtém a instância registrada do tipo [T].
  T get<T extends Object>() {
    return _getIt.get<T>();
  }

  @override
  /// Verifica se o tipo [T] está registrado.
  bool isRegistered<T extends Object>() {
    return _getIt.isRegistered<T>();
  }

  /// Reseta todas as registrações do container (útil em testes).
  Future<void> reset() async {
    await _getIt.reset();
  }
}
