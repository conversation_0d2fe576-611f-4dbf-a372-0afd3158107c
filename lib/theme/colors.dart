import 'package:flutter/material.dart';

/// Paleta de cores do aplicativo
/// Contém todas as cores definidas no design system
class AppColors {
  // ===== BRANDING =====
  /// Paleta de cores que diz respeito à identidade visual do projeto
  
  /// Primary - Botões e elementos de destaque
  static const Color primary = Color(0xFF78942B);
  
  /// Primary pressed - Elementos como botões e ícones pressionados
  static const Color primaryPressed = Color(0xFF506D00);
  
  /// Primary light - Backgrounds
  static const Color primaryLight = Color(0xFFE5EDD0);
  
  /// Secondary - Complemento da interface
  static const Color secondary = Color(0xFF000000);
  
  /// Gradient colors - Textos de destaque
  static const Color gradientStart = Color(0xFF78942B);
  static const Color gradientEnd = Color(0xFF3F5504);
  
  // ===== NEUTRAS =====
  /// Cores neutras que serão utilizadas no decorrer da interface como textos e fundo
  
  /// Black - Textos
  static const Color black = Color(0xFF111827);
  
  /// White - Backgrounds
  static const Color white = Color(0xFFFFFFFF);
  
  /// Gray 0 - Hover de elementos
  static const Color gray0 = Color(0xFFEFF4F5);
  
  /// Gray 1 - Backgrounds de elementos
  static const Color gray1 = Color(0xFFF9FAFB);
  
  /// Gray 2 - Linhas separatórias
  static const Color gray2 = Color(0xFFF3F4F6);
  
  /// Gray 3 - Stroke tabelas e inputs
  static const Color gray3 = Color(0xFFE5E7EB);
  
  /// Gray 4 - Strokes e backgrounds
  static const Color gray4 = Color(0xFFD1D5DB);
  
  /// Gray 5 - Textos de apoio
  static const Color gray5 = Color(0xFF6B7280);
  
  /// Gray 6 - Elementos bloqueados e strokes
  static const Color gray6 = Color(0xFF929292);
  
  // ===== FEEDBACK =====
  /// Cores semafóricas, sinalizam diferentes status de ações ou elementos em geral do aplicativo
  
  // Success
  /// Success - Ações bem sucedidas
  static const Color success = Color(0xFF34D399);
  
  /// Success texts - Texto de contraste de ações bem sucedidas
  static const Color successTexts = Color(0xFF065F46);
  
  /// Success boxes - Background de caixas de ações bem sucedidas
  static const Color successBoxes = Color(0xFFECFDF5);
  
  // Warning
  /// Warning - Alertas e pendências
  static const Color warning = Color(0xFFFBBF24);
  
  /// Warning texts - Texto de contraste de alertas e pendências
  static const Color warningTexts = Color(0xFF92400E);
  
  /// Warning boxes - Background de caixas de alertas e pendências
  static const Color warningBoxes = Color(0xFFFFFBEB);
  
  // Error
  /// Error - Erros e exclusões
  static const Color error = Color(0xFFDC2626);
  
  /// Error texts - Texto de contraste de erros e exclusões
  static const Color errorTexts = Color(0xFF991B1B);
  
  /// Error boxes - Background de caixas de erros e exclusões
  static const Color errorBoxes = Color(0xFFFEF2F2);
  
  // System info
  /// System info - Informações do sistema
  static const Color systemInfo = Color(0xFF60A5FA);
  
  /// System info texts - Texto de contraste de informações do sistema
  static const Color systemInfoTexts = Color(0xFF1E40AF);
  
  /// System info boxes - Background de caixas de informações do sistema
  static const Color systemInfoBoxes = Color(0xFFEFF6FF);
  
  // ===== GRADIENTS =====
  /// Gradient linear para textos de destaque
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [gradientStart, gradientEnd],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
}
