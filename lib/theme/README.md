# Tema (Design System - Core Tokens)

Este diretório consolida tokens de cor e a configuração de `ThemeData` usados em toda a aplicação.

## Estrutura

| Arquivo | Função |
|---------|--------|
| `colors.dart` | Declara a paleta (`AppColors`) e gradientes fixos |
| `app_theme.dart` | Cria o `ThemeData` principal (`AppTheme.lightTheme`) e estiliza componentes base do Material 3 |
| `theme.dart` | Barrel export (facilita import único) |

## Importação Rápida

```dart
import 'theme/theme.dart'; // passa a ter AppColors e AppTheme
```

## Aplicação no `MaterialApp`

```dart
MaterialApp(
  theme: AppTheme.lightTheme,
  home: const MyHome(),
);
```

## Paleta (AppColors)

### Branding
- primary (#78942B)
- primaryPressed (#506D00)
- primaryLight (#E5EDD0)
- secondary (#000000)
- gradientStart (#78942B) / gradientEnd (#3F5504) → `AppColors.primaryGradient`

### Neutras
- black (#111827)
- white (#FFFFFF)
- gray0–gray6 (hover, backgrounds, strokes, textos de apoio, disabled)

### Feedback / Status
- success / successTexts / successBoxes
- warning / warningTexts / warningBoxes
- error / errorTexts / errorBoxes
- systemInfo / systemInfoTexts / systemInfoBoxes

### Padrões de Uso
- Sufixo `Texts`: cor de contraste sobre a cor principal.
- Sufixo `Boxes`: backgrounds suaves para mensagens / badges.

## Mapeamento no ColorScheme

| ColorScheme | Valor | Observação |
|-------------|-------|-----------|
| primary | AppColors.primary | Botões / elementos de destaque |
| onPrimary | AppColors.white | Texto sobre primary |
| primaryContainer | AppColors.primaryLight | Superfícies derivadas |
| onPrimaryContainer | AppColors.primaryPressed | Texto sobre container primary |
| secondary | AppColors.gray5 | Textos secundários |
| onSecondary | AppColors.white | Contraste secundário |
| secondaryContainer | AppColors.gray1 | Superfícies suaves |
| onSecondaryContainer | AppColors.black | Texto sobre container secundário |
| surface / background | AppColors.white | Layout base |
| onSurface / onBackground | AppColors.black | Texto principal |
| surfaceVariant | AppColors.gray1 | Blocos / cards neutros |
| onSurfaceVariant | AppColors.gray5 | Texto secundário em superfícies |
| error | AppColors.error | Ações destrutivas |
| onError | AppColors.white | Texto sobre erro |
| errorContainer | AppColors.errorBoxes | Fundo de alerta de erro |
| onErrorContainer | AppColors.errorTexts | Texto contraste em container erro |
| outline | AppColors.gray3 | Borda padrão inputs/cards |
| outlineVariant | AppColors.gray4 | Borda alternativa / hover |
| shadow | black 10% | Elevação leve |
| scrim | black 50% | Overlays / dialogs |
| inverseSurface | AppColors.black | Inversão para chips / barras |
| onInverseSurface | AppColors.white | Texto sobre inverso |
| inversePrimary | AppColors.primaryLight | Uso pontual em estados invertidos |

## Componentes com Theme Customizado

- AppBar (`_appBarTheme`): cores neutras, sem elevação forte.
- ElevatedButton / TextButton / OutlinedButton: estilizados mas recomenda-se o uso de `ZuzButton` para consistência (o tema base cobre fallback Material padrão).
- Inputs (`InputDecorationTheme`): bordas, foco e erro alinhados aos tokens.
- CardTheme: raio 12, leve elevação.
- DividerTheme: usa `gray2`.

## Uso dos Tokens

```dart
// Cor direta
Container(color: AppColors.primaryLight);

// Texto com cor semântica
Text('Erro', style: TextStyle(color: AppColors.errorTexts));

// Gradient em texto
ShaderMask(
  shaderCallback: (r) => AppColors.primaryGradient.createShader(r),
  child: const Text('Título', style: TextStyle(color: Colors.white)),
);
```

## Estendendo o Tema

```dart
final theme = AppTheme.lightTheme.copyWith(
  snackBarTheme: const SnackBarThemeData(
    backgroundColor: AppColors.black,
    contentTextStyle: TextStyle(color: AppColors.white),
  ),
);

MaterialApp(theme: theme);
```

Para tokens adicionais (ex: espaçamentos, tipografia), crie uma classe `AppSpacing`, `AppTextStyles` ou use `ThemeExtension`:

```dart
@immutable
class RadiusTokens extends ThemeExtension<RadiusTokens> {
  const RadiusTokens({required this.small, required this.medium});
  final double small;
  final double medium;
  @override
  RadiusTokens copyWith({double? small, double? medium}) => RadiusTokens(
    small: small ?? this.small,
    medium: medium ?? this.medium,
  );
  @override
  ThemeExtension<RadiusTokens> lerp(ThemeExtension<RadiusTokens>? other, double t) => this;
}

final theme = AppTheme.lightTheme.copyWith(extensions: const [RadiusTokens(small: 4, medium: 12)]);
```

Recuperando:
```dart
final radius = Theme.of(context).extension<RadiusTokens>()?.small ?? 4;
```

## Integração com ZuzButton

O `ZuzButton` NÃO depende do `ElevatedButtonTheme`, mas usa diretamente os tokens em `AppColors`. Ao alterar cores em `AppColors`, revise também `lib/components/button/button.dart` para garantir contraste adequado.

## Boas Práticas

- Centralize novas cores em `AppColors` (evite hex solto em widgets).
- Prefira nomes semânticos (ex: `errorBoxes`) a novos sufixos ambíguos.
- Evite mutabilidade: mantenha tudo como `const`.
- Para Dark Mode futuro, duplique a lógica de `_lightColorScheme` para `_darkColorScheme` e exponha `darkTheme`.

## Roadmap
- Dark theme completo
- Tipografia escalável (AppTextStyles centralizado)
- ThemeExtensions para spacing / radius / opacidades

---
Mantenha este README sincronizado ao modificar tokens ou estrutura do tema.
