import 'package:flutter/material.dart';
import 'colors.dart';

/// Configuração do tema da aplicação usando Material Design 3
class AppTheme {
  /// Tema claro da aplicação
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: _lightColorScheme,
      appBarTheme: _appBarTheme,
      elevatedButtonTheme: _elevatedButtonTheme,
      textButtonTheme: _textButtonTheme,
      outlinedButtonTheme: _outlinedButtonTheme,
      inputDecorationTheme: _inputDecorationTheme,
      cardTheme: _cardTheme,
      dividerTheme: _dividerTheme,
      scaffoldBackgroundColor: AppColors.white,
      fontFamily: 'Century Gothic',
    );
  }

  /// Color Scheme para o tema claro
  static ColorScheme get _lightColorScheme {
    return ColorScheme.light(
      // Cores principais
      primary: AppColors.primary,
      onPrimary: AppColors.white,
      primaryContainer: AppColors.primaryLight,
      onPrimaryContainer: AppColors.primaryPressed,
      
      // Cores secundárias
      secondary: AppColors.gray5,
      onSecondary: AppColors.white,
      secondaryContainer: AppColors.gray1,
      onSecondaryContainer: AppColors.black,
      
      // Cores de superfície
      surface: AppColors.white,
      onSurface: AppColors.black,
      surfaceVariant: AppColors.gray1,
      onSurfaceVariant: AppColors.gray5,
      
      // Cores de fundo
      background: AppColors.white,
      onBackground: AppColors.black,
      
      // Cores de erro
      error: AppColors.error,
      onError: AppColors.white,
      errorContainer: AppColors.errorBoxes,
      onErrorContainer: AppColors.errorTexts,
      
      // Cores de contorno
      outline: AppColors.gray3,
      outlineVariant: AppColors.gray4,
      
      // Cores de sombra e sobreposição
      shadow: AppColors.black.withOpacity(0.1),
      scrim: AppColors.black.withOpacity(0.5),
      
      // Cores inversas
      inverseSurface: AppColors.black,
      onInverseSurface: AppColors.white,
      inversePrimary: AppColors.primaryLight,
    );
  }

  /// Tema da AppBar
  static AppBarTheme get _appBarTheme {
    return AppBarTheme(
      backgroundColor: AppColors.white,
      foregroundColor: AppColors.black,
      elevation: 0,
      scrolledUnderElevation: 1,
      surfaceTintColor: AppColors.primary,
      titleTextStyle: const TextStyle(
        color: AppColors.black,
        fontSize: 18,
        fontWeight: FontWeight.w600,
      ),
      iconTheme: const IconThemeData(
        color: AppColors.black,
      ),
    );
  }

  /// Tema dos botões elevados
  static ElevatedButtonThemeData get _elevatedButtonTheme {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        disabledBackgroundColor: AppColors.gray4,
        disabledForegroundColor: AppColors.gray6,
        elevation: 2,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        textStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ).copyWith(
        overlayColor: MaterialStateProperty.resolveWith<Color?>(
          (Set<MaterialState> states) {
            if (states.contains(MaterialState.pressed)) {
              return AppColors.primaryPressed;
            }
            if (states.contains(MaterialState.hovered)) {
              return AppColors.primaryLight;
            }
            return null;
          },
        ),
      ),
    );
  }

  /// Tema dos botões de texto
  static TextButtonThemeData get _textButtonTheme {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: AppColors.primary,
        disabledForegroundColor: AppColors.gray6,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        textStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// Tema dos botões outlined
  static OutlinedButtonThemeData get _outlinedButtonTheme {
    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColors.primary,
        disabledForegroundColor: AppColors.gray6,
        side: const BorderSide(color: AppColors.primary, width: 1),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        textStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ).copyWith(
        side: MaterialStateProperty.resolveWith<BorderSide?>(
          (Set<MaterialState> states) {
            if (states.contains(MaterialState.disabled)) {
              return const BorderSide(color: AppColors.gray6, width: 1);
            }
            return const BorderSide(color: AppColors.primary, width: 1);
          },
        ),
      ),
    );
  }

  /// Tema dos inputs
  static InputDecorationTheme get _inputDecorationTheme {
    return InputDecorationTheme(
      filled: true,
      fillColor: AppColors.white,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.gray3, width: 1),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.gray3, width: 1),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.primary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.error, width: 1),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.error, width: 2),
      ),
      disabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.gray4, width: 1),
      ),
      labelStyle: const TextStyle(
        color: AppColors.gray5,
        fontSize: 16,
      ),
      hintStyle: const TextStyle(
        color: AppColors.gray5,
        fontSize: 16,
      ),
      errorStyle: const TextStyle(
        color: AppColors.error,
        fontSize: 14,
      ),
    );
  }

  /// Tema dos cards
  static CardThemeData get _cardTheme {
    return CardThemeData(
      color: AppColors.white,
      surfaceTintColor: AppColors.primary,
      elevation: 2,
      shadowColor: AppColors.black.withOpacity(0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      margin: const EdgeInsets.all(8),
    );
  }

  /// Tema dos divisores
  static DividerThemeData get _dividerTheme {
    return const DividerThemeData(
      color: AppColors.gray2,
      thickness: 1,
      space: 1,
    );
  }
}
