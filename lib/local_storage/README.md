# Mó<PERSON>lo `local_storage`

Responsável por persistência local segura de valores simples (chave/valor) utilizando `flutter_secure_storage`. Fornece uma interface (`LocalStorageService`) e uma implementação padrão (`LocalStorageServiceImplementation`), além do enum `LocalStorageKey` para chaves tipadas e centralizadas.

## Princípios

- Interface primeiro: `LocalStorageService` define as operações de armazenamento e é consumida pelos repositórios.
- Implementação segura: `LocalStorageServiceImplementation` usa `FlutterSecureStorage` (com `AndroidOptions(encryptedSharedPreferences: true)` no Android).
- Chaves centralizadas: `LocalStorageKey` concentra as chaves persistidas (ex.: `sessionToken`, `locale`).
- Serialização única: valores são serializados/deserializados em JSON internamente, mantendo tipagem genérica no consumo (`setValue<T>`, `getValue<T>`).

## Estrutura

- `models/local_storage_key.dart`: enum de chaves suportadas.
- `services/local_storage_service.dart`: contrato do serviço de armazenamento local.
- `services/local_storage_service_implementation.dart`: implementação baseada em `flutter_secure_storage`.

## API

`LocalStorageService` expõe:
- `Future<void> setValue<T>(LocalStorageKey key, T value)`
- `Future<T?> getValue<T>(LocalStorageKey key)`
- `Future<bool> hasKey(LocalStorageKey key)`
- `Future<void> removeValue(LocalStorageKey key)`
- `Future<void> clear()`

## Uso típico

```dart
final storage = LocalStorageServiceImplementation();
await storage.setValue(LocalStorageKey.sessionToken, 'abc123');
final token = await storage.getValue<String>(LocalStorageKey.sessionToken);
```

## Boas práticas

- Evite strings livres para chaves; adicione entradas em `LocalStorageKey`.
- Armazene apenas dados necessários e não sensíveis quando possível.
- Remova chaves obsoletas com `removeValue` em fluxos de logout ou limpeza.
- Mantenha o serviço injetado por DI (ex.: `get_it`) para facilitar testes.
