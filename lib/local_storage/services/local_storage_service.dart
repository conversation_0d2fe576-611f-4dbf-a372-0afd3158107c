import '../models/local_storage_key.dart';

/// Contrato para persistência local (chave/valor).
///
/// Os valores devem ser acessados usando chaves de `LocalStorageKey` e
/// tipagem genérica para leitura/escrita (`setValue<T>`, `getValue<T>`).
/// Implementações são livres para serializar os dados (ex.: JSON).
abstract class LocalStorageService {
  /// Armazena um valor para a [key] informada.
  Future<void> setValue<T>(LocalStorageKey key, T value);

  /// Recupera um valor associado à [key].
  ///
  /// Retorna `null` quando a chave não existe.
  Future<T?> getValue<T>(LocalStorageKey key);

  /// Verifica se existe valor persistido para a [key].
  Future<bool> hasKey(LocalStorageKey key);

  /// Remove o valor associado à [key].
  Future<void> removeValue(LocalStorageKey key);

  /// Remove todos os valores armazenados.
  Future<void> clear();
}
