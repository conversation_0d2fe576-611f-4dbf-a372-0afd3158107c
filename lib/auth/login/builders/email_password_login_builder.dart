import 'package:flutter/material.dart';

import '../../../components/text_field/zuz_text_field.dart';
import '../../../locator/locator.dart';
import '../states/email_password_login_state.dart';
import '../views/email_password_login_page.dart';

class EmailPasswordLoginBuilder extends StatelessWidget {
  const EmailPasswordLoginBuilder({super.key});

  EmailPasswordLoginState get _state =>
      Locator.instance.get<EmailPasswordLoginState>();

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _state as ChangeNotifier,
      builder: (context, child) {
        return EmailPasswordLoginPage(
          formKey: _state.formKey,
          autovalidateMode: _state.autovalidateMode,
          emailInput: ZuzTextField(
            label: _state.strings.emailLabel,
            keyboardType: TextInputType.emailAddress,
            onChanged: _state.onEmailChanged,
            validator: _state.validateEmail,
          ),
          passwordInput: ZuzTextField(
            label: _state.strings.passwordLabel,
            isPassword: true,
            onChanged: _state.onPasswordChanged,
            validator: _state.validatePassword,
          ),
          resetPasswordButton: TextButton(
            onPressed: () => _state.resetPassword(),
            child: Text(_state.strings.resetPasswordButtonLabel),
          ),
          keepMeLoggedInInput: CheckboxListTile(
            value: _state.keepMeLoggedIn,
            onChanged: (value) => _state.onKeepMeLoggedInChanged(value!),
            title: Text(_state.strings.keepMeLoggedInLabel),
          ),
          loginButton: FilledButton(
            onPressed: () => _state.loginAction.execute(),
            child: _state.loginAction.isRunning
                ? const SizedBox.square(
                    dimension: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Text(_state.strings.loginButtonLabel),
          ),
        );
      },
    );
  }
}
