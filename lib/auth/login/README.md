# <PERSON>ó<PERSON>lo `auth/login`

Fluxo de autenticação com e-mail e senha. Segue o padrão modular do projeto (Services → Repositories → States → Builders → Views), mantendo responsabilidades bem definidas e testáveis.

## Princípios

- Interface primeiro: `LoginRepository`, `EmailPasswordLoginService` e `SessionStorageService` expõem contratos claros.
- SSOT no repositório: `LoginRepositoryImplementation` centraliza regras de autenticação e persistência de sessão.
- Estado isolado: `EmailPasswordLoginState` coordena UI e aciona o repositório; sem lógica de rede direta na View.
- Builder dedicado: `EmailPasswordLoginBuilder` conecta o `State` à `View`.
- Limpeza de estado por rota: utilize `onExit` da rota para resetar singletons do fluxo de login quando sair do fluxo.

## Estrutura

- `services/email_password_login_service.dart`: contrato do serviço remoto de login.
- `services/email_password_login_service_implementation.dart`: chama a API e retorna `LoginResponse` ou lança `LoginError`.
- `services/session_storage_sevice.dart`: contrato para armazenar sessão/token localmente.
- `services/session_storage_service_implementation.dart`: implementação (ex.: `SharedPreferences`, `SecureStorage`).
- `repositories/login_repository.dart`: contrato com `isAuthenticated`, `loginWithEmailAndPassword()` e `logout()`.
- `repositories/login_repository_implementation.dart`: orquestra serviços de rede e storage, expõe SSOT para o `State` e implementa `logout()` limpando cache, header de autorização e token persistido.
- `states/email_password_login_state.dart`: contrato do estado (campos de formulário, loading, erros, ações de submit).
- `states/email_password_login_state_implementation.dart`: validação, controle de loading/erro e chamada ao repositório.
- `builders/email_password_login_builder.dart`: injeta dependências e entrega o `State` para a `View`.
- `views/email_password_login_page.dart`: layout da tela de login (formulário, botões, feedback de erro/loading).
- `views/login_text_field.dart`: widget de input reutilizável para campos de login.
- `models/`: modelos auxiliares
  - `email_password_credentials.dart`
  - `login_response.dart`
  - `login_error.dart`
  - `login_strings.dart`, `login_strings_pt_br.dart`

## Fluxo

1. Usuário preenche e-mail/senha na `EmailPasswordLoginPage`.
2. A View chama ações do `EmailPasswordLoginState` (ex.: `submit()`), que valida e controla loading/erro.
3. O `State` chama `LoginRepository.loginWithEmailAndPassword(...)` com `EmailPasswordCredentials`.
4. O repositório usa `EmailPasswordLoginService` para autenticar e, se `storeToken == true`, persiste via `SessionStorageService`.
5. Em caso de sucesso, o `State` atualiza UI e pode disparar navegação via `NavigationRepository` (fora deste módulo).
6. Ao sair do fluxo (ex.: trocar de rota), use `onExit` na rota para `resetLazySingleton<EmailPasswordLoginState>()`.

Observações sobre logout:

- `logout()` no `LoginRepository`:
  - define o cache interno `_isAuthenticated` para `false` imediatamente (sem aguardar storage);
  - chama `EmailPasswordLoginService.removeAuthorizationHeader()`;
  - tenta remover o token via `SessionStorageService.removeToken()` e ignora erros de storage.

## Exemplo de uso (State → Repository)

```dart
await loginRepository.loginWithEmailAndPassword(
  email: state.email,
  password: state.password,
  storeToken: state.rememberMe,
);
```

### Exemplo de uso (Logout e checagem de sessão)

```dart
// Fazer logout
await loginRepository.logout();

// Consultar status de autenticação (usa cache quando disponível)
final authenticated = await loginRepository.isAuthenticated;
```

## Boas práticas

- Mantenha a `View` livre de regras de negócio; delegue ao `State` e ao `Repository`.
- Centralize erros de autenticação em `LoginError` e traduções em `login_strings*.dart`.
- Não acesse serviços diretamente no `State`; sempre via `LoginRepository`.
- Proteja dados sensíveis no storage (use `SecureStorage` quando aplicável).
- Prefira injeção via `Locator` e singletons lazy para `State` e `Repository`.
- Em `logout()`, não propague exceções de storage; garanta remoção do header e invalidar cache local imediatamente.

## Testes (sugestão)

- Mock de `EmailPasswordLoginService` para simular respostas de sucesso/erro.
- Mock de `SessionStorageService` para cobrir persistência de sessão.
- Testes de `EmailPasswordLoginStateImplementation` para validação, loading e tratamento de erros.
- Testes de `LoginRepositoryImplementation.logout()` cobrindo:
  - remoção do header de autorização;
  - remoção do token no storage (e tolerância a erros);
  - atualização do cache de autenticação sem reconsultar o storage.
