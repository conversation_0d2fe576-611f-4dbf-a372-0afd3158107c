import 'package:flutter/material.dart';

class EmailPasswordLoginPage extends StatelessWidget {
  const EmailPasswordLoginPage({
    super.key,
    required this.formKey,
    required this.autovalidateMode,
    required this.emailInput,
    required this.passwordInput,
    required this.keepMeLoggedInInput,
    required this.resetPasswordButton,
    required this.loginButton,
  });

  final GlobalKey<FormState> formKey;
  final AutovalidateMode autovalidateMode;
  final Widget emailInput;
  final Widget passwordInput;
  final Widget keepMeLoggedInInput;
  final Widget resetPasswordButton;
  final Widget loginButton;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Login')),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: formKey,
          autovalidateMode: autovalidateMode,
          child: SingleChildScrollView(
            child: Column(
              children: [
                // Email field
                emailInput,

                // Password field
                const SizedBox(height: 16),
                passwordInput,

                // Keep me logged in
                const SizedBox(height: 16),
                keepMeLoggedInInput,

                // Reset password button
                const SizedBox(height: 24),
                resetPasswordButton,

                // Login button
                const SizedBox(height: 24),
                loginButton,
              ],
            ),
          ),
        ),
      ),
    );
  }
}
