import 'package:flutter/material.dart';

class LoginTextField extends StatelessWidget {
  const LoginTextField({
    super.key,
    required this.label,
    required this.keyboardType,
    required this.onChanged,
    this.obscureText = false,
    this.validator,
  });

  final String label;
  final TextInputType keyboardType;
  final void Function(String) onChanged;
  final bool obscureText;
  final String? Function(String?)? validator;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      decoration: InputDecoration(labelText: label),
      keyboardType: keyboardType,
      onChanged: onChanged,
      obscureText: obscureText,
      validator: validator,
    );
  }
}
