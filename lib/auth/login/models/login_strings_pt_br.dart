import 'login_strings.dart';

class LoginStringsPtBr implements LoginStrings {
  @override
  String get emailLabel => 'E-mail';

  @override
  String get passwordLabel => 'Senha';

  @override
  String get keepMeLoggedInLabel => 'Manter conectado';

  @override
  String get resetPasswordButtonLabel => 'Esqueci minha senha';

  @override
  String get loginButtonLabel => 'Entrar';

  @override
  String get invalidEmailError => 'Informe o e-mail';

  @override
  String get invalidPasswordError => 'Informe a senha';

  @override
  String get credentialsNotFound => 'E-mail e/ou senha inválidos';

  @override
  String get unknownError => 'Erro desconhecido';
}
