import 'package:zuz_app/localization/models/strings.dart';

import '../../../error/models/app_error.dart';

enum LoginError implements AppError {
  credentialsNotFound(),
  unknown();

  @override
  String messageFromStrings(Strings strings) {
    return switch (this) {
      credentialsNotFound => strings.loginStrings.credentialsNotFound,
      unknown => strings.loginStrings.unknownError,
    };
  }
}
