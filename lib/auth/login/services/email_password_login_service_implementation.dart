import '../../../http_client/models/http_error.dart';
import '../../../http_client/models/http_method.dart';
import '../../../http_client/services/http_client_service.dart';
import '../models/email_password_credentials.dart';
import '../models/login_error.dart';
import '../models/login_response.dart';
import 'email_password_login_service.dart';

class EmailPasswordLoginServiceImplementation implements EmailPasswordLoginService {
  EmailPasswordLoginServiceImplementation({required HttpClientService httpClient})
    : _httpClient = httpClient;

  final HttpClientService _httpClient;

  @override
  Future<LoginResponse> login(EmailPasswordCredentials credentials) async {
    try {
      final response = await _httpClient.request(
        url: '/auth/signin',
        method: HttpMethod.post,
        body: credentials.toJson(),
      );
      return LoginResponse.fromJson(response);
    } on HttpError catch (error) {
      if (error == HttpError.badRequest) throw LoginError.credentialsNotFound;
      if (error == HttpError.notFound) throw LoginError.credentialsNotFound;
      throw LoginError.unknown;
    }
  }

  @override
  void setAuthorizationHeader(String token) {
    _httpClient.setAuthorizationHeader(token);
  }

  @override
  void removeAuthorizationHeader() {
    _httpClient.removeAuthorizationHeader();
  }
}
