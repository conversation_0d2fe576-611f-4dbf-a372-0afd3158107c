
import '../../../local_storage/models/local_storage_key.dart';
import '../../../local_storage/services/local_storage_service.dart';
import 'session_storage_sevice.dart';

class SessionStorageServiceImplementation implements SessionStorageService {
  final LocalStorageService _localStorage;

  SessionStorageServiceImplementation({required LocalStorageService localStorage}) : _localStorage = localStorage;

  @override
  Future<void> saveToken(String token) async {
    await _localStorage.setValue(LocalStorageKey.sessionToken, token);
  }

  @override
  Future<String?> getToken() async {
    return await _localStorage.getValue<String>(LocalStorageKey.sessionToken);
  }

  @override
  Future<void> removeToken() async {
    await _localStorage.removeValue(LocalStorageKey.sessionToken);
  }
}
