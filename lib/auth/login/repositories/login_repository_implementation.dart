import '../models/email_password_credentials.dart';
import '../services/email_password_login_service.dart';
import '../services/session_storage_sevice.dart';
import 'login_repository.dart';

class LoginRepositoryImplementation extends LoginRepository {
  LoginRepositoryImplementation({
    required EmailPasswordLoginService loginService,
    required SessionStorageService sessionService,
  })  : _loginService = loginService,
        _sessionService = sessionService;

  final EmailPasswordLoginService _loginService;
  final SessionStorageService _sessionService;

  bool? _isAuthenticated;

  @override
  Future<bool> get isAuthenticated async {
    // Status is cached
    if (_isAuthenticated != null) return _isAuthenticated!;

    // No status cached, fetch from storage
    await _fetchToken();
    return _isAuthenticated ?? false;
  }

  Future<void> _fetchToken() async {
    try {
      final token = await _sessionService.getToken();
      _isAuthenticated = token != null && token.isNotEmpty;
    } catch (_) {
      // Ignores storage error
    }
  }

  @override
  Future<void> loginWithEmailAndPassword({
    required String email,
    required String password,
    required bool storeToken,
  }) async {
    final response = await _loginService.login(EmailPasswordCredentials(email: email, password: password));
    _isAuthenticated = true;
    _loginService.setAuthorizationHeader(response.token);
    if (storeToken) {
      _saveToken(response.token);
    } else {
      _deleteToken();
    }
  }

  @override
  Future<void> logout() async {
    _isAuthenticated = false;
    _loginService.removeAuthorizationHeader();
    _deleteToken();
  }

  Future<void> _saveToken(String token) async {
    try {
      await _sessionService.saveToken(token);
    } catch (_) {
      // Ignores storage error
    }
  }

  Future<void> _deleteToken() async {
    try {
      await _sessionService.removeToken();
    } catch (_) {
      // Ignores storage error
    }
  }
}
