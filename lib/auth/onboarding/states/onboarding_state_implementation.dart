import 'package:flutter/material.dart';

import '../../../localization/repositories/locale_repository.dart';
import '../../../navigation/models/onboarding_route.dart';
import '../../../navigation/repositories/navigation_repository.dart';
import '../models/onboarding_strings.dart';
import 'onboarding_state.dart';

class OnboardingStateImplementation extends ChangeNotifier
    implements OnboardingState {
  OnboardingStateImplementation({
    required LocaleRepository localeRepository,
    required NavigationRepository navigationRepository,
  }) : _localeRepository = localeRepository,
       _navigationRepository = navigationRepository;

  final LocaleRepository _localeRepository;
  final NavigationRepository _navigationRepository;

  @override
  OnboardingStrings get strings => _localeRepository.strings.onboardingStrings;

  var _logoScale = 0.0;
  @override
  double get logoScale => _logoScale;

  @override
  Duration get logoScaleAnimationDuration => const Duration(milliseconds: 350);

  var _logoAlignment = Alignment.center;
  @override
  Alignment get logoAlignment => _logoAlignment;

  @override
  Duration get logoAlignmentAnimationDuration => const Duration(milliseconds: 400);

  @override
  double get contentOpacity => _logoAlignment == Alignment.center ? 0 : 1;

  @override
  Duration get contentOpacityAnimationDuration => const Duration(milliseconds: 400);

  @override
  Future<void> startLogoAnimation() async {
    _logoScale = 1;
    notifyListeners();
    await Future.delayed(logoScaleAnimationDuration);
    await Future.delayed(Duration(milliseconds: 500));
    _logoAlignment = Alignment.topCenter;
    notifyListeners();
    Future.delayed(logoAlignmentAnimationDuration);
  }

  @override
  void goToLogin() {
    _navigationRepository.to(LoginRoute());
  }

  @override
  void goToFirstAccess() {
    _navigationRepository.to(FirstAccessRoute());
  }

  @override
  void goToSignUp() {}
}
