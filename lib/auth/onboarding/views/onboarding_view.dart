import 'package:flutter/material.dart';
import 'package:zuz_app/components/button/button.dart';
import 'package:zuz_app/navigation/models/onboarding_route.dart';

class OnboardingView extends StatelessWidget {
  const OnboardingView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Onboarding')),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Colum<PERSON>(
          spacing : 16,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            <PERSON>uz<PERSON><PERSON>on(
              onPressed: () => LoginRoute().go(context),
              text:'Login',
            ),
            <PERSON>uz<PERSON><PERSON>on(
              onPressed: () => FirstAccessRoute().go(context),
              text:'First Access',
            ),
            <PERSON>uz<PERSON>utton(
              onPressed: () => ComponentsExamplesRoute().go(context),
              text:'Exemplos de Componentes',
            ),
          ],
        ),
      ),
    );
  }
}
