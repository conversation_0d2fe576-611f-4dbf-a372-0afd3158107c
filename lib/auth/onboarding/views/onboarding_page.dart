import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../../components/button/button.dart';
import '../../../components/scaffold/scaffold_with_centered_body.dart';
import '../../../navigation/models/onboarding_route.dart';
import '../../../theme/colors.dart';

class OnboardingPage extends StatelessWidget {
  const OnboardingPage({
    super.key,
    required this.topBar,
    required this.welcomeText,
    required this.loginButtonLabel,
    required this.onLoginPressed,
    required this.firstAccessButtonLabel,
    required this.onFirstAccessPressed,
    required this.signUpText,
    required this.onSignUpPressed,
  });

  final AppBar topBar;
  final String welcomeText;
  final String loginButtonLabel;
  final VoidCallback onLoginPressed;
  final String firstAccessButtonLabel;
  final VoidCallback onFirstAccessPressed;
  final String signUpText;
  final VoidCallback onSignUpPressed;

  @override
  Widget build(BuildContext context) {
    return ScaffoldWithCenteredBody(
      topBar: topBar,
      backgroundColor: Colors.transparent,
      showPatternOverBackground: false,
      body: Column(
        children: [
          // Texto de boas vindas
          Container(alignment: Alignment.centerLeft, child: Text(welcomeText)),

          // Botão de login
          const SizedBox(height: 40),
          ZuzButton(
            variant: ZuzButtonVariant.primary,
            width: double.infinity,
            text: loginButtonLabel,
            onPressed: onLoginPressed,
          ),

          // Botão de primeiro acesso
          const SizedBox(height: 16),
          ZuzButton(
            variant: ZuzButtonVariant.secondary,
            width: double.infinity,
            text: firstAccessButtonLabel,
            onPressed: onFirstAccessPressed,
          ),

          if (kReleaseMode)
            // Exemplos
            Padding(
              padding: const EdgeInsets.only(top: 16),
              child: ZuzButton(
                variant: ZuzButtonVariant.primary,
                width: double.infinity,
                text: 'Exemplos de Componentes',
                onPressed: () => ComponentsExamplesRoute().go(context),
              ),
            ),
        ],
      ),

      // Link para cadastro
      footer: Column(
        children: [
          SizedBox(width: 156, child: Divider(color: AppColors.gray6)),
          const SizedBox(height: 40),
          Text(signUpText),
          const SizedBox(height: 40),
        ],
      ),
    );
  }
}
