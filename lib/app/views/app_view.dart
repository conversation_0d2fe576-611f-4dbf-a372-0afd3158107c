import 'package:flutter/material.dart';

class AppView extends StatelessWidget {
  const AppView({
    super.key,
    required this.routerConfig,
    required this.lightTheme,
    required this.locale,
    required this.supportedLocales,
    required this.localizationsDelegates,
  });

  final RouterConfig<Object> routerConfig;
  final ThemeData lightTheme;
  final Locale locale;
  final List<Locale> supportedLocales;
  final List<LocalizationsDelegate> localizationsDelegates;

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      routerConfig: routerConfig,
      theme: lightTheme,
      locale: locale,
      supportedLocales: supportedLocales,
      localizationsDelegates: localizationsDelegates,
      debugShowCheckedModeBanner: false,
    );
  }
}
