# <PERSON><PERSON><PERSON><PERSON> `app`

Orquestra a configuração principal do aplicativo (tema, navegação e localização) e expõe o widget raiz via `AppBuilder`.

## Princípios

- Interface primeiro: `AppState` define o contrato do estado global do app.
- Composição: `AppStateImplementation` agrega `NavigationRepository` (router) e `LocaleRepository` (i18n).
- Builder dedicado: `AppBuilder` conecta o `AppState` à `AppView` e injeta as dependências do módulo `locator`.

## Estrutura

- `states/app_state.dart`: contrato do estado do app (router, tema, locale, delegados, locais suportados).
- `states/app_state_implementation.dart`: implementação que lê dados de `LocaleRepository` e `NavigationRepository`.
- `builders/app_builder.dart`: widget raiz que monta a `AppView` com configurações do `AppState`.
- `views/app_view.dart`: widget com `MaterialApp.router` (tema, localização, rotas).

## Fluxo

1. `main.dart` chama `initializeDependencies()` e roda `AppBuilder()`.
2. `AppBuilder` obtém `AppState` do `Locator` e passa seus dados para `AppView`.
3. `AppView` constrói o `MaterialApp.router` com:
   - `routerConfig` do `NavigationRepository`.
   - `lightTheme` do `AppTheme`.
   - Localização de `LocaleRepository` (locale atual, delegados e locais suportados).

## Exemplo (main)

```dart
void main() {
  initializeDependencies();
  runApp(AppBuilder());
}
```

## Boas práticas

- Mantenha `AppState` livre de lógica de negócios; use repositories para dados e regras.
- Injete `AppState` via `Locator` e prefira lazy singletons.
- Centralize alterações de tema/locale no `LocaleRepository` e `AppTheme`.
