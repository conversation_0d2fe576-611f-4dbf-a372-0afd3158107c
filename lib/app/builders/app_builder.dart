import 'package:flutter/material.dart';

import '../../locator/locator.dart';
import '../states/app_state.dart';
import '../views/app_view.dart';

class AppBuilder extends StatelessWidget {
  const AppBuilder({super.key});

  AppState get _state => Locator.instance.get<AppState>();

  @override
  Widget build(BuildContext context) {
    return AppView(
      routerConfig: _state.routerConfig,
      lightTheme: _state.lightTheme,
      locale: _state.locale,
      supportedLocales: _state.supportedLocales,
      localizationsDelegates: _state.localizationsDelegates,
    );
  }
}
