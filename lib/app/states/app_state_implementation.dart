import 'package:flutter/material.dart';

import '../../localization/repositories/locale_repository.dart';
import '../../navigation/repositories/navigation_repository.dart';
import '../../theme/app_theme.dart';
import 'app_state.dart';

/// Implementação de [AppState] que compõe repositórios centrais.
///
/// - Navegação: delega `routerConfig` ao `NavigationRepository`.
/// - Localização: expõe `locale`, `supportedLocales` e `localizationsDelegates`
///   a partir do `LocaleRepository`.
/// - Tema: utiliza `AppTheme.lightTheme`.
class AppStateImplementation implements AppState {
  AppStateImplementation({
    required LocaleRepository localeRepository,
    required NavigationRepository navigationRepository,
  }) : _localeRepository = localeRepository,
       _navigationRepository = navigationRepository;

  final LocaleRepository _localeRepository;
  final NavigationRepository _navigationRepository;

  @override
  /// Configuração de rotas vinda do `NavigationRepository`.
  RouterConfig<Object> get routerConfig => _navigationRepository.routerConfig;

  @override
  /// Tema claro padrão definido em `AppTheme`.
  ThemeData get lightTheme => AppTheme.lightTheme;

  @override
  /// Locale atual do app exposto pelo `LocaleRepository`.
  Locale get locale => _localeRepository.currentLocale;

  @override
  /// Lista de locales suportados.
  List<Locale> get supportedLocales => _localeRepository.supportedLocales;

  @override
  /// Delegados de localização do Flutter + do app.
  List<LocalizationsDelegate> get localizationsDelegates =>
      _localeRepository.localizationsDelegates;
}
