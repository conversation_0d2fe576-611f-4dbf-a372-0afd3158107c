import 'package:flutter/material.dart';

/// Contrato do estado global do aplicativo.
///
/// Fornece configurações para o `MaterialApp.router`, tema e localização.
abstract class AppState {
  /// Configuração de rotas do app (GoRouter).
  RouterConfig<Object> get routerConfig;
  /// Tema claro padrão do app.
  ThemeData get lightTheme;
  /// Locale atual do app.
  Locale get locale;
  /// Lista de locales suportados.
  List<Locale> get supportedLocales;
  /// Delegados de localização.
  List<LocalizationsDelegate> get localizationsDelegates;
}