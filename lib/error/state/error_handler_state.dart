/// Contrato para o estado responsável por exibir mensagens de erro.
///
/// Implementações devem transformar um erro (idealmente um `AppError`)
/// em uma mensagem amigável ao usuário, respeitando o idioma atual.
abstract class ErrorHandlerState {
  /// Exibe uma mensagem para o [error] informado.
  ///
  /// - Se for `AppError`, usa `messageFromStrings` para resolver o texto.
  /// - <PERSON><PERSON><PERSON> contr<PERSON><PERSON>, utiliza uma mensagem genérica (ex.: `unknownError`).
  void showMessage(dynamic error);
}
