import 'package:flutter/widgets.dart';

import '../../components/modal/toast.dart';
import '../../localization/models/strings.dart';
import '../../localization/repositories/locale_repository.dart';
import '../../navigation/repositories/navigation_repository.dart';
import '../models/app_error.dart';
import 'error_handler_state.dart';

/// Implementação padrão de `ErrorHandlerState`.
///
/// - Resolve a mensagem de erro conforme o idioma atual (i18n) a partir
///   de uma instância de `AppError`.
/// - Quando o erro não implementa `AppError`, utiliza a mensagem genérica
///   `unknownError` do idioma atual.
/// - Exibe o texto via `Toast` respeitando o ciclo de vida do `BuildContext`.
class ErrorHandlerStateImplementation implements ErrorHandlerState {
  ErrorHandlerStateImplementation({
    required NavigationRepository navigationRepository,
    required LocaleRepository localeRepository,
  }) : _navigationRepository = navigationRepository,
       _localeRepository = localeRepository;

  final NavigationRepository _navigationRepository;
  final LocaleRepository _localeRepository;
  BuildContext? get context => _navigationRepository.context;
  Strings get strings => _localeRepository.strings;

  @override
  /// Exibe uma mensagem de erro ao usuário.
  ///
  /// - Se [error] for `AppError`, a mensagem é obtida por
  ///   `error.messageFromStrings(strings)`.
  /// - Caso contrário, utiliza `strings.errorStrings.unknownError`.
  /// - Não faz nada se o `context` não estiver montado.
  void showMessage(dynamic error) {
    if (context?.mounted != true) return;
    var message = strings.errorStrings.unknownError;
    if (error is AppError) message = error.messageFromStrings(strings);
    showToast(context: context!, message: message);
  }
}
