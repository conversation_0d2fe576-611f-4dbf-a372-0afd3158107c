import '../../localization/models/strings.dart';

/// Interface base para erros do aplicativo.
///
/// Cada módulo deve definir seus erros implementando `AppError`,
/// garantindo que a mensagem exibida ao usuário seja resolvida a partir
/// das strings do idioma atual e mantendo a separação de responsabilidades.
abstract class AppError implements Exception {
  /// Retorna a mensagem do erro traduzida conforme o idioma atual.
  ///
  /// Utilize `strings` para acessar as chaves i18n do domínio correspondente
  /// (por exemplo, `strings.errorStrings.unknownError`).
  String messageFromStrings(Strings strings);
}
