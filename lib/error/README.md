# <PERSON><PERSON><PERSON><PERSON> `error`

Este módulo centraliza a padronização de erros do aplicativo e a forma como mensagens são exibidas ao usuário, seguindo a arquitetura modular do projeto.

## Regras e princípios

- Todos os erros conhecidos lançados devem ser do tipo `AppError`.
- Cada módulo deve implementar a interface `AppError` para que o erro possa ser exibido corretamente.
- `ErrorHandlerState` exibe a mensagem de erro a partir do `AppError` informado.
- A responsabilidade de tratar cada caso de erro fica com cada módulo; o `ErrorHandlerState` se limita a exibir a mensagem de erro.

Essas regras garantem a separação de responsabilidades e o uso consistente de mensagens (internacionalizadas) em toda a aplicação.

## Estrutura

- `models/app_error.dart`: Interface base para erros do app.
- `models/error_strings.dart`: Contrato das strings de erros (por idioma).
- `models/error_strings_pt_br.dart`: Implementação pt_BR das strings de erros.
- `state/error_handler_state.dart`: Interface do estado responsável por exibir mensagens de erro.
- `state/error_handler_state_implementation.dart`: Implementação padrão do `ErrorHandlerState` que resolve a mensagem e exibe um toast.

## Fluxo de funcionamento

1. Um módulo detecta uma situação de erro e lança uma instância que implementa `AppError`.
2. A camada de State que captura o erro chama `ErrorHandlerState.showMessage(error)`.
3. A implementação (`ErrorHandlerStateImplementation`) verifica se o erro implementa `AppError`.
   - Se sim, resolve a mensagem pelo `Strings` atual (i18n) via `messageFromStrings`.
   - Se não, utiliza a mensagem genérica `unknownError` do idioma atual.
4. A mensagem é exibida ao usuário (ex.: por `Toast`).

## Internacionalização (i18n)

- O método `AppError.messageFromStrings(Strings strings)` deve retornar uma mensagem já traduzida com base no idioma corrente.
- O contrato de mensagens de erro por idioma está em `ErrorStrings` e as implementações específicas (como `ErrorStringsPtBr`) definem os textos.

## Como criar um novo erro

1. No seu módulo, crie uma classe que implemente `AppError`.
2. Implemente `messageFromStrings(Strings strings)` retornando a mensagem adequada ao caso.
3. Lance esse erro quando necessário.

Exemplo:

```dart
import 'package:zuz_app/localization/models/strings.dart';
import 'package:zuz_app/error/models/app_error.dart';

class LoginInvalidCredentialsError implements AppError {
  @override
  String messageFromStrings(Strings strings) {
    return strings.authStrings.invalidCredentials; // exemplo de chave i18n
  }
}
```

## Como exibir o erro

Chame `ErrorHandlerState.showMessage(error)` a partir do seu State/Builder. A implementação cuidará de resolver a mensagem e exibir o toast.

```dart
try {
  await repository.login(user, pass);
} catch (error) {
  errorHandlerState.showMessage(error);
}
```

## Boas práticas

- Mantenha erros específicos por domínio/módulo (evite usar `Exception` genérica).
- Garanta mensagens claras e orientadas ao usuário final.
- Prefira reutilizar chaves i18n existentes para consistência.
- Não acople a exibição à regra de negócio; deixe isso para o `ErrorHandlerState`.
