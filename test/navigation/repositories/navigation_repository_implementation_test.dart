import 'package:flutter/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';
import 'package:mocktail/mocktail.dart';
import 'package:zuz_app/localization/models/strings.dart';
import 'package:zuz_app/navigation/models/navigation_error.dart';
import 'package:zuz_app/navigation/models/onboarding_route.dart';
import 'package:zuz_app/navigation/repositories/navigation_repository_implementation.dart';

class MockGoRouteData extends Mock implements GoRouteData {}

class MockBuildContext extends Mock implements BuildContext {}

class MockStrings extends Mock implements Strings {}

void main() {
  setUpAll(() {
    registerFallbackValue(MockBuildContext());
  });

  group('NavigationRepositoryImplementation', () {
    late NavigationRepositoryImplementation repository;

    setUp(() {
      repository = NavigationRepositoryImplementation();
    });

    group('constructor', () {
      test('creates instance with proper initialization', () {
        // Act
        final repo = NavigationRepositoryImplementation();

        // Assert
        expect(repo, isA<NavigationRepositoryImplementation>());
        expect(repo.key, isA<GlobalKey<NavigatorState>>());
        expect(repo.routerConfig, isA<GoRouter>());
      });
    });

    group('key', () {
      test('returns a GlobalKey<NavigatorState>', () {
        // Act
        final key = repository.key;

        // Assert
        expect(key, isA<GlobalKey<NavigatorState>>());
        expect(key, isNotNull);
      });

      test('returns the same key instance on multiple calls', () {
        // Act
        final key1 = repository.key;
        final key2 = repository.key;

        // Assert
        expect(key1, equals(key2));
        expect(identical(key1, key2), isTrue);
      });
    });

    group('context', () {
      test('returns null when navigator key has no current context', () {
        // Act
        final context = repository.context;

        // Assert
        expect(context, isNull);
      });

      test('returns context when navigator key has current context', () {
        // This test would require a widget test environment to properly test
        // the context retrieval from the navigator key
        expect(repository.context, repository.key?.currentContext);
      });
    });

    group('routerConfig', () {
      test('returns a GoRouter instance', () {
        // Act
        final config = repository.routerConfig;

        // Assert
        expect(config, isA<GoRouter>());
      });

      test('router config uses the repository key as navigator key', () {
        // Act
        final config = repository.routerConfig as GoRouter;

        // Assert
        expect(config.routerDelegate.navigatorKey, equals(repository.key));
      });

      test('router config has correct initial location', () {
        // Act
        final config = repository.routerConfig as GoRouter;

        // Assert
        expect(config.routeInformationParser.configuration.routes, isNotEmpty);
        // The initial location should be the onboarding route
        expect(config.routeInformationProvider.value.uri.toString(), 
               equals(const OnboardingRoute().location));
      });

      test('router config includes all expected route groups', () {
        // Act
        final config = repository.routerConfig as GoRouter;
        final routes = config.routeInformationParser.configuration.routes;

        // Assert
        expect(routes, isNotEmpty);
        // We expect routes from all the imported route modules:
        // onboarding, assessments, herd, properties, packages, team, account
        expect(routes.length, greaterThan(0));
      });
    });

    group('to', () {
      late MockGoRouteData mockRoute;

      setUp(() {
        mockRoute = MockGoRouteData();
      });

      test('throws NavigationError.nullContext when context is null', () {
        // Arrange
        // Context is null by default in test environment

        // Act & Assert
        expect(
          () => repository.to(mockRoute),
          throwsA(equals(NavigationError.nullContext)),
        );

        // Verify that go was never called
        verifyNever(() => mockRoute.go(any()));
      });

      test('calls route.go with context when context is available', () {
        // This test would require a widget test environment to properly test
        // navigation with a real context. In a unit test environment, we can
        // only test the null context scenario.
        
        // For now, we'll test the method signature and behavior
        expect(() => repository.to(mockRoute), throwsA(isA<NavigationError>()));
      });

      test('propagates exceptions from route.go', () {
        // This would require a more complex setup with a real context
        // and mocked route behavior, which is better suited for integration tests
      });
    });

    group('integration scenarios', () {
      test('repository maintains consistent state across multiple calls', () {
        // Act
        final key1 = repository.key;
        final config1 = repository.routerConfig;
        final context1 = repository.context;

        final key2 = repository.key;
        final config2 = repository.routerConfig;
        final context2 = repository.context;

        // Assert
        expect(key1, equals(key2));
        expect(identical(key1, key2), isTrue); // Same key instance
        expect(context1, equals(context2));
        
        // Router configs are new instances but should have same configuration
        expect(config1, isA<GoRouter>());
        expect(config2, isA<GoRouter>());
        expect((config1 as GoRouter).routerDelegate.navigatorKey, equals(key1));
        expect((config2 as GoRouter).routerDelegate.navigatorKey, equals(key2));
      });

      test('different repository instances have different keys', () {
        // Arrange
        final repo1 = NavigationRepositoryImplementation();
        final repo2 = NavigationRepositoryImplementation();

        // Act
        final key1 = repo1.key;
        final key2 = repo2.key;

        // Assert
        expect(key1, isNot(equals(key2)));
        expect(identical(key1, key2), isFalse);
      });
    });

    group('NavigationError', () {
      test('nullContext error has correct message', () {
        // Arrange
        final mockStrings = MockStrings();
        
        // Act
        final error = NavigationError.nullContext;
        final message = error.messageFromStrings(mockStrings);

        // Assert
        expect(message, equals('BuildContext is null'));
      });
    });
  });
}
