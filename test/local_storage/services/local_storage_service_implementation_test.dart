import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:zuz_app/local_storage/models/local_storage_key.dart';
import 'dart:convert';

import 'package:zuz_app/local_storage/services/local_storage_service_implementation.dart';

class MockSecureStorage extends Mock implements FlutterSecureStorage {}

class _TestObject {
  _TestObject(this.a, this.b, this.c);
  final int a;
  final double b;
  final String c;

  @override
  bool operator ==(Object other) {
    if (other is! _TestObject) return false;
    return a == other.a && b == other.b && c == other.c;
  }

  @override
  int get hashCode => Object.hash(a, b, c);

  Map<String, dynamic> toJson() {
    return {'a': a, 'b': b, 'c': c};
  }

  factory _TestObject.fromJson(Map<String, dynamic> json) {
    return _TestObject(json['a'], json['b'], json['c']);
  }
}

void main() {
  late MockSecureStorage mockStorage;
  late LocalStorageServiceImplementation storage;
  final key = LocalStorageKey.sessionToken;

  setUp(() {
    mockStorage = MockSecureStorage();
    storage = LocalStorageServiceImplementation(storage: mockStorage);
  });

  String serializeValue<T>(T value) => json.encode({'value': value});

  void mockStorageRead(String key, String? returnValue) {
    when(
      () => mockStorage.read(key: key),
    ).thenAnswer((_) => Future<String?>.value(returnValue));
  }

  group('getValue', () {
    test('retrieves object json', () async {
      final object = _TestObject(1, 2.0, '3');
      final serializedValue = serializeValue(object.toJson());

      // Mock the read operation to return the serialized value
      mockStorageRead(key.name, serializedValue);

      // Retrieve the value
      final result = await storage.getValue<Map<String, dynamic>>(key) ?? {};

      // Verify the retrieved value matches the stored value
      expect(_TestObject.fromJson(result), equals(object));
    });

    test('retrieves object list json', () async {
      final objectList = [
        _TestObject(1, 2.0, '3'),
        _TestObject(4, 5.0, '6'),
        _TestObject(7, 8.0, '9'),
      ];
      final serializedValue = serializeValue(
        objectList.map((object) => object.toJson()).toList(),
      );

      // Mock the read operation to return the serialized value
      mockStorageRead(key.name, serializedValue);

      // Retrieve the value
      final result = await storage.getValue<List<dynamic>>(key) ?? [];
      final resultList = result
          .map((object) => _TestObject.fromJson(object))
          .toList();

      // Verify the retrieved value matches the stored value
      expect(resultList, equals(objectList));
    });
  });
}
