import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:zuz_app/http_client/models/http_error.dart';
import 'package:zuz_app/http_client/models/http_method.dart';
import 'package:zuz_app/http_client/services/http_client_service_implementation.dart';

class MockDio extends Mock implements Dio {}

class MockBaseOptions extends Mock implements BaseOptions {}

class MockInterceptors extends Mock implements Interceptors {}

class MockResponse extends Mock implements Response<dynamic> {}

class FakeInterceptor extends Fake implements Interceptor {}

class FakeBaseOptions extends Fake implements BaseOptions {}

class FakeOptions extends Fake implements Options {}

class FakeRequestOptions extends Fake implements RequestOptions {}

void main() {
  setUpAll(() {
    registerFallbackValue(FakeInterceptor());
    registerFallbackValue(FakeBaseOptions());
    registerFallbackValue(FakeOptions());
    registerFallbackValue(FakeRequestOptions());
  });
  late MockDio mockDio;
  late MockBaseOptions mockBaseOptions;
  late MockInterceptors mockInterceptors;
  late HttpClientServiceImplementation httpClient;

  setUp(() {
    mockDio = MockDio();
    mockBaseOptions = MockBaseOptions();
    mockInterceptors = MockInterceptors();
    
    // Setup default mocks
    when(() => mockDio.options).thenReturn(mockBaseOptions);
    when(() => mockDio.interceptors).thenReturn(mockInterceptors);
    when(() => mockInterceptors.add(any())).thenReturn(null);
    when(() => mockBaseOptions.headers).thenReturn(<String, dynamic>{});
    
    httpClient = HttpClientServiceImplementation(dio: mockDio);
  });

  group('HttpClientServiceImplementation', () {
    group('constructor', () {
      test('should create instance with default Dio when none provided', () {
        final client = HttpClientServiceImplementation();
        expect(client, isA<HttpClientServiceImplementation>());
      });

      test('should configure Dio with provided parameters', () {
        const baseUrl = 'https://api.example.com';
        const connectTimeout = Duration(seconds: 10);
        const receiveTimeout = Duration(seconds: 15);
        const sendTimeout = Duration(seconds: 20);

        HttpClientServiceImplementation(
          dio: mockDio,
          baseUrl: baseUrl,
          connectTimeout: connectTimeout,
          receiveTimeout: receiveTimeout,
          sendTimeout: sendTimeout,
        );

        verify(() => mockDio.options = any(
          that: isA<BaseOptions>()
              .having((o) => o.baseUrl, 'baseUrl', baseUrl)
              .having((o) => o.connectTimeout, 'connectTimeout', connectTimeout)
              .having((o) => o.receiveTimeout, 'receiveTimeout', receiveTimeout)
              .having((o) => o.sendTimeout, 'sendTimeout', sendTimeout),
        )).called(1);
      });

      test('should configure default headers and add interceptor', () {
        // Create a fresh mock for this test
        final freshMockDio = MockDio();
        final freshMockBaseOptions = MockBaseOptions();
        final freshMockInterceptors = MockInterceptors();

        when(() => freshMockDio.options).thenReturn(freshMockBaseOptions);
        when(() => freshMockDio.interceptors).thenReturn(freshMockInterceptors);
        when(() => freshMockInterceptors.add(any())).thenReturn(null);

        HttpClientServiceImplementation(dio: freshMockDio);

        // Verify that options were set (we can't easily verify the exact content due to BaseOptions complexity)
        verify(() => freshMockDio.options = any(that: isA<BaseOptions>())).called(1);

        // Verify that LogInterceptor was added
        verify(() => freshMockInterceptors.add(any(that: isA<LogInterceptor>()))).called(1);
      });
    });

    group('request', () {
      test('should make successful GET request', () async {
        const url = '/test';
        const responseData = {'message': 'success'};
        final mockResponse = MockResponse();
        
        when(() => mockResponse.data).thenReturn(responseData);
        when(() => mockDio.request(
          url,
          data: any(named: 'data'),
          options: any(named: 'options'),
        )).thenAnswer((_) async => mockResponse);

        final result = await httpClient.request(
          url: url,
          method: HttpMethod.get,
        );

        expect(result, equals(responseData));
        verify(() => mockDio.request(
          url,
          data: null,
          options: any(
            named: 'options',
            that: isA<Options>().having((o) => o.method, 'method', 'GET'),
          ),
        )).called(1);
      });

      test('should make successful POST request with body', () async {
        const url = '/test';
        const requestBody = {'name': 'test'};
        const responseData = {'id': 1, 'name': 'test'};
        final mockResponse = MockResponse();
        
        when(() => mockResponse.data).thenReturn(responseData);
        when(() => mockDio.request(
          url,
          data: any(named: 'data'),
          options: any(named: 'options'),
        )).thenAnswer((_) async => mockResponse);

        final result = await httpClient.request(
          url: url,
          method: HttpMethod.post,
          body: requestBody,
        );

        expect(result, equals(responseData));
        verify(() => mockDio.request(
          url,
          data: requestBody,
          options: any(
            named: 'options',
            that: isA<Options>().having((o) => o.method, 'method', 'POST'),
          ),
        )).called(1);
      });

      test('should include custom headers in request', () async {
        const url = '/test';
        const headers = {'Authorization': 'Bearer token123'};
        final mockResponse = MockResponse();
        
        when(() => mockResponse.data).thenReturn({});
        when(() => mockDio.request(
          url,
          data: any(named: 'data'),
          options: any(named: 'options'),
        )).thenAnswer((_) async => mockResponse);

        await httpClient.request(
          url: url,
          method: HttpMethod.get,
          headers: headers,
        );

        verify(() => mockDio.request(
          url,
          data: null,
          options: any(
            named: 'options',
            that: isA<Options>().having((o) => o.headers, 'headers', headers),
          ),
        )).called(1);
      });
    });

    group('error handling', () {
      test('should throw HttpError.connection for connection timeout', () async {
        const url = '/test';
        final dioError = DioException(
          requestOptions: RequestOptions(path: url),
          type: DioExceptionType.connectionTimeout,
        );

        when(() => mockDio.request(
          url,
          data: any(named: 'data'),
          options: any(named: 'options'),
        )).thenThrow(dioError);

        expect(
          () => httpClient.request(url: url, method: HttpMethod.get),
          throwsA(isA<HttpError>().having((e) => e, 'error', HttpError.connection)),
        );
      });

      test('should throw HttpError.connection for send timeout', () async {
        const url = '/test';
        final dioError = DioException(
          requestOptions: RequestOptions(path: url),
          type: DioExceptionType.sendTimeout,
        );

        when(() => mockDio.request(
          url,
          data: any(named: 'data'),
          options: any(named: 'options'),
        )).thenThrow(dioError);

        expect(
          () => httpClient.request(url: url, method: HttpMethod.get),
          throwsA(isA<HttpError>().having((e) => e, 'error', HttpError.connection)),
        );
      });

      test('should throw HttpError.connection for receive timeout', () async {
        const url = '/test';
        final dioError = DioException(
          requestOptions: RequestOptions(path: url),
          type: DioExceptionType.receiveTimeout,
        );

        when(() => mockDio.request(
          url,
          data: any(named: 'data'),
          options: any(named: 'options'),
        )).thenThrow(dioError);

        expect(
          () => httpClient.request(url: url, method: HttpMethod.get),
          throwsA(isA<HttpError>().having((e) => e, 'error', HttpError.connection)),
        );
      });

      test('should throw HttpError.connection for connection error', () async {
        const url = '/test';
        final dioError = DioException(
          requestOptions: RequestOptions(path: url),
          type: DioExceptionType.connectionError,
        );

        when(() => mockDio.request(
          url,
          data: any(named: 'data'),
          options: any(named: 'options'),
        )).thenThrow(dioError);

        expect(
          () => httpClient.request(url: url, method: HttpMethod.get),
          throwsA(isA<HttpError>().having((e) => e, 'error', HttpError.connection)),
        );
      });

      test('should throw appropriate HttpError for bad response with status code', () async {
        const url = '/test';
        final mockResponse = MockResponse();
        when(() => mockResponse.statusCode).thenReturn(404);

        final dioError = DioException(
          requestOptions: RequestOptions(path: url),
          type: DioExceptionType.badResponse,
          response: mockResponse,
        );

        when(() => mockDio.request(
          url,
          data: any(named: 'data'),
          options: any(named: 'options'),
        )).thenThrow(dioError);

        expect(
          () => httpClient.request(url: url, method: HttpMethod.get),
          throwsA(isA<HttpError>().having((e) => e, 'error', HttpError.notFound)),
        );
      });

      test('should throw HttpError.unknown for cancel exception', () async {
        const url = '/test';
        final dioError = DioException(
          requestOptions: RequestOptions(path: url),
          type: DioExceptionType.cancel,
        );

        when(() => mockDio.request(
          url,
          data: any(named: 'data'),
          options: any(named: 'options'),
        )).thenThrow(dioError);

        expect(
          () => httpClient.request(url: url, method: HttpMethod.get),
          throwsA(isA<HttpError>().having((e) => e, 'error', HttpError.unknown)),
        );
      });

      test('should throw HttpError.unknown for unknown exception', () async {
        const url = '/test';
        final dioError = DioException(
          requestOptions: RequestOptions(path: url),
          type: DioExceptionType.unknown,
        );

        when(() => mockDio.request(
          url,
          data: any(named: 'data'),
          options: any(named: 'options'),
        )).thenThrow(dioError);

        expect(
          () => httpClient.request(url: url, method: HttpMethod.get),
          throwsA(isA<HttpError>().having((e) => e, 'error', HttpError.unknown)),
        );
      });

      test('should throw HttpError.unknown for bad certificate', () async {
        const url = '/test';
        final dioError = DioException(
          requestOptions: RequestOptions(path: url),
          type: DioExceptionType.badCertificate,
        );

        when(() => mockDio.request(
          url,
          data: any(named: 'data'),
          options: any(named: 'options'),
        )).thenThrow(dioError);

        expect(
          () => httpClient.request(url: url, method: HttpMethod.get),
          throwsA(isA<HttpError>().having((e) => e, 'error', HttpError.unknown)),
        );
      });

      test('should throw HttpError.unknown for non-DioException', () async {
        const url = '/test';

        when(() => mockDio.request(
          url,
          data: any(named: 'data'),
          options: any(named: 'options'),
        )).thenThrow(Exception('Some other error'));

        expect(
          () => httpClient.request(url: url, method: HttpMethod.get),
          throwsA(isA<HttpError>().having((e) => e, 'error', HttpError.unknown)),
        );
      });
    });

    group('utility methods', () {
      test('get should call request with GET method', () async {
        const url = '/test';
        const headers = {'Custom': 'header'};
        const responseData = {'data': 'test'};
        final mockResponse = MockResponse();

        when(() => mockResponse.data).thenReturn(responseData);
        when(() => mockDio.request(
          url,
          data: any(named: 'data'),
          options: any(named: 'options'),
        )).thenAnswer((_) async => mockResponse);

        final result = await httpClient.get(url, headers: headers);

        expect(result, equals(responseData));
        verify(() => mockDio.request(
          url,
          data: null,
          options: any(
            named: 'options',
            that: isA<Options>()
                .having((o) => o.method, 'method', 'GET')
                .having((o) => o.headers, 'headers', headers),
          ),
        )).called(1);
      });

      test('post should call request with POST method and body', () async {
        const url = '/test';
        const body = {'name': 'test'};
        const headers = {'Custom': 'header'};
        const responseData = {'id': 1};
        final mockResponse = MockResponse();

        when(() => mockResponse.data).thenReturn(responseData);
        when(() => mockDio.request(
          url,
          data: any(named: 'data'),
          options: any(named: 'options'),
        )).thenAnswer((_) async => mockResponse);

        final result = await httpClient.post(url, body: body, headers: headers);

        expect(result, equals(responseData));
        verify(() => mockDio.request(
          url,
          data: body,
          options: any(
            named: 'options',
            that: isA<Options>()
                .having((o) => o.method, 'method', 'POST')
                .having((o) => o.headers, 'headers', headers),
          ),
        )).called(1);
      });

      test('put should call request with PUT method and body', () async {
        const url = '/test';
        const body = {'name': 'updated'};
        const headers = {'Custom': 'header'};
        const responseData = {'id': 1, 'name': 'updated'};
        final mockResponse = MockResponse();

        when(() => mockResponse.data).thenReturn(responseData);
        when(() => mockDio.request(
          url,
          data: any(named: 'data'),
          options: any(named: 'options'),
        )).thenAnswer((_) async => mockResponse);

        final result = await httpClient.put(url, body: body, headers: headers);

        expect(result, equals(responseData));
        verify(() => mockDio.request(
          url,
          data: body,
          options: any(
            named: 'options',
            that: isA<Options>()
                .having((o) => o.method, 'method', 'PUT')
                .having((o) => o.headers, 'headers', headers),
          ),
        )).called(1);
      });

      test('delete should call request with DELETE method', () async {
        const url = '/test';
        const headers = {'Custom': 'header'};
        const responseData = {'deleted': true};
        final mockResponse = MockResponse();

        when(() => mockResponse.data).thenReturn(responseData);
        when(() => mockDio.request(
          url,
          data: any(named: 'data'),
          options: any(named: 'options'),
        )).thenAnswer((_) async => mockResponse);

        final result = await httpClient.delete(url, headers: headers);

        expect(result, equals(responseData));
        verify(() => mockDio.request(
          url,
          data: null,
          options: any(
            named: 'options',
            that: isA<Options>()
                .having((o) => o.method, 'method', 'DELETE')
                .having((o) => o.headers, 'headers', headers),
          ),
        )).called(1);
      });
    });

    group('authorization methods', () {
      test('setAuthorizationHeader should add Bearer token to headers', () {
        const token = 'abc123';
        final headers = <String, dynamic>{};
        when(() => mockBaseOptions.headers).thenReturn(headers);

        httpClient.setAuthorizationHeader(token);

        expect(headers['Authorization'], equals('Bearer $token'));
      });

      test('removeAuthorizationHeader should remove Authorization header', () {
        final headers = <String, dynamic>{'Authorization': 'Bearer token123'};
        when(() => mockBaseOptions.headers).thenReturn(headers);

        httpClient.removeAuthorizationHeader();

        expect(headers.containsKey('Authorization'), isFalse);
      });
    });

    group('_getMethodString', () {
      test('should return correct string for each HttpMethod', () {
        // We can't directly test private methods, but we can test them indirectly
        // through the public request method
        final testCases = [
          (HttpMethod.get, 'GET'),
          (HttpMethod.post, 'POST'),
          (HttpMethod.put, 'PUT'),
          (HttpMethod.delete, 'DELETE'),
        ];

        for (final (method, expectedString) in testCases) {
          final mockResponse = MockResponse();
          when(() => mockResponse.data).thenReturn({});
          when(() => mockDio.request(
            any(),
            data: any(named: 'data'),
            options: any(named: 'options'),
          )).thenAnswer((_) async => mockResponse);

          httpClient.request(url: '/test', method: method);

          verify(() => mockDio.request(
            any(),
            data: any(named: 'data'),
            options: any(
              named: 'options',
              that: isA<Options>().having((o) => o.method, 'method', expectedString),
            ),
          )).called(1);

          reset(mockDio);
          when(() => mockDio.options).thenReturn(mockBaseOptions);
          when(() => mockDio.interceptors).thenReturn(mockInterceptors);
        }
      });
    });
  });
}
