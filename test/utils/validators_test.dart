import 'package:flutter_test/flutter_test.dart';
import 'package:zuz_app/utils/validators.dart';

void main() {
  group('Validators', () {
    group('validateRequired', () {
      test('returns true for non-empty string', () {
        // Arrange
        const value = 'test';

        // Act
        final result = Validators.validateRequired(value);

        // Assert
        expect(result, isTrue);
      });

      test('returns true for string with whitespace', () {
        // Arrange
        const value = ' ';

        // Act
        final result = Validators.validateRequired(value);

        // Assert
        expect(result, isTrue);
      });

      test('returns true for string with multiple characters', () {
        // Arrange
        const value = 'hello world';

        // Act
        final result = Validators.validateRequired(value);

        // Assert
        expect(result, isTrue);
      });

      test('returns false for null value', () {
        // Arrange
        const String? value = null;

        // Act
        final result = Validators.validateRequired(value);

        // Assert
        expect(result, isFalse);
      });

      test('returns false for empty string', () {
        // Arrange
        const value = '';

        // Act
        final result = Validators.validateRequired(value);

        // Assert
        expect(result, isFalse);
      });
    });

    group('validateEmail', () {
      test('returns true for valid email addresses', () {
        // Arrange
        final validEmails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>', // Consecutive dots are allowed by this regex
          '<EMAIL>', // Trailing dot in local part is allowed
        ];

        for (final email in validEmails) {
          // Act
          final result = Validators.validateEmail(email);

          // Assert
          expect(result, isTrue, reason: 'Email "$email" should be valid');
        }
      });

      test('returns false for invalid email addresses', () {
        // Arrange
        final invalidEmails = [
          'invalid',
          'invalid@',
          '@invalid.com',
          'invalid@.com',
          'invalid@com',
          'invalid.@com',
          'invalid@com.',
          'invalid@.com.',

          '<EMAIL>',
          'invalid@',
          '@',
          '',
          'test@',
          '@test.com',
          'test@.com',
          'test@com',

          'test@test.',
          'test@test.c',
          '<EMAIL>',
          'test <EMAIL>',
          'test@test space.com',
          '<EMAIL>', // Plus signs not allowed by this regex
        ];

        for (final email in invalidEmails) {
          // Act
          final result = Validators.validateEmail(email);

          // Assert
          expect(result, isFalse, reason: 'Email "$email" should be invalid');
        }
      });

      test('returns false for null email', () {
        // Arrange
        const String? email = null;

        // Act
        final result = Validators.validateEmail(email);

        // Assert
        expect(result, isFalse);
      });

      test('returns false for empty email', () {
        // Arrange
        const email = '';

        // Act
        final result = Validators.validateEmail(email);

        // Assert
        expect(result, isFalse);
      });

      test('handles edge cases correctly', () {
        // Test email with numbers
        expect(Validators.validateEmail('<EMAIL>'), isTrue);

        // Test email with hyphens
        expect(Validators.validateEmail('<EMAIL>'), isTrue);

        // Test email with underscores
        expect(Validators.validateEmail('test_user@test_domain.com'), isTrue);

        // Test email with dots in local part
        expect(Validators.validateEmail('<EMAIL>'), isTrue);

        // Test that plus signs are not allowed by this regex
        expect(Validators.validateEmail('<EMAIL>'), isFalse);

        // Test minimum valid email format
        expect(Validators.validateEmail('<EMAIL>'), isTrue);
        expect(Validators.validateEmail('<EMAIL>'), isTrue);

        // Test that consecutive dots are actually allowed by this regex
        expect(Validators.validateEmail('<EMAIL>'), isTrue);
      });

      test('validates domain extension length correctly', () {
        // Valid extensions (2-4 characters)
        expect(Validators.validateEmail('<EMAIL>'), isTrue);
        expect(Validators.validateEmail('<EMAIL>'), isTrue);
        expect(Validators.validateEmail('<EMAIL>'), isTrue);
        
        // Invalid extensions (too short or too long)
        expect(Validators.validateEmail('test@example.c'), isFalse);
        expect(Validators.validateEmail('<EMAIL>'), isFalse);
      });
    });
  });
}
