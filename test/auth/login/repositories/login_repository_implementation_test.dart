import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:zuz_app/auth/login/models/email_password_credentials.dart';
import 'package:zuz_app/auth/login/models/login_response.dart';
import 'package:zuz_app/auth/login/repositories/login_repository_implementation.dart';
import 'package:zuz_app/auth/login/services/email_password_login_service.dart';
import 'package:zuz_app/auth/login/services/session_storage_sevice.dart';

class MockEmailPasswordLoginService extends Mock implements EmailPasswordLoginService {}
class MockSessionStorageService extends Mock implements SessionStorageService {}

class FakeEmailPasswordCredentials extends Fake implements EmailPasswordCredentials {}

void main() {
  setUpAll(() {
    registerFallbackValue(FakeEmailPasswordCredentials());
  });
  group('LoginRepositoryImplementation', () {
    late MockEmailPasswordLoginService mockLoginService;
    late MockSessionStorageService mockSessionService;
    late LoginRepositoryImplementation repository;

    setUp(() {
      mockLoginService = MockEmailPasswordLoginService();
      mockSessionService = MockSessionStorageService();
      repository = LoginRepositoryImplementation(
        loginService: mockLoginService,
        sessionService: mockSessionService,
      );
    });

    group('constructor', () {
      test('creates instance with required services', () {
        // Act
        final repo = LoginRepositoryImplementation(
          loginService: mockLoginService,
          sessionService: mockSessionService,
        );

        // Assert
        expect(repo, isA<LoginRepositoryImplementation>());
      });
    });

    group('storeToken behavior', () {
      const testEmail = '<EMAIL>';
      const testPassword = 'password123';
      const testToken = 'login_response_token';

      test('removes token from session storage when storeToken is false', () async {
        // Arrange
        final loginResponse = LoginResponse(token: testToken);
        when(() => mockLoginService.login(any()))
            .thenAnswer((_) async => loginResponse);
        when(() => mockSessionService.removeToken())
            .thenAnswer((_) async {});
        when(() => mockLoginService.setAuthorizationHeader(any()))
            .thenReturn(null);

        // Act
        await repository.loginWithEmailAndPassword(
          email: testEmail,
          password: testPassword,
          storeToken: false,
        );

        // Assert
        verify(() => mockSessionService.removeToken()).called(1);
        verifyNever(() => mockSessionService.saveToken(any()));
      });
    });

    group('isAuthenticated', () {
      test('returns cached authentication status when available', () async {
        // Arrange - First call to set cache
        when(() => mockSessionService.getToken())
            .thenAnswer((_) async => 'cached_token');
        
        // Act - First call to populate cache
        final firstResult = await repository.isAuthenticated;
        
        // Act - Second call should use cache
        final secondResult = await repository.isAuthenticated;

        // Assert
        expect(firstResult, isTrue);
        expect(secondResult, isTrue);
        // getToken should only be called once (for cache population)
        verify(() => mockSessionService.getToken()).called(1);
      });

      test('returns true when token exists in storage', () async {
        // Arrange
        when(() => mockSessionService.getToken())
            .thenAnswer((_) async => 'valid_token');

        // Act
        final result = await repository.isAuthenticated;

        // Assert
        expect(result, isTrue);
        verify(() => mockSessionService.getToken()).called(1);
      });

      test('returns false when no token exists in storage', () async {
        // Arrange
        when(() => mockSessionService.getToken())
            .thenAnswer((_) async => null);

        // Act
        final result = await repository.isAuthenticated;

        // Assert
        expect(result, isFalse);
        verify(() => mockSessionService.getToken()).called(1);
      });

      test('returns false when empty token exists in storage', () async {
        // Arrange
        when(() => mockSessionService.getToken())
            .thenAnswer((_) async => '');

        // Act
        final result = await repository.isAuthenticated;

        // Assert
        expect(result, isFalse);
        verify(() => mockSessionService.getToken()).called(1);
      });

      test('returns false when storage throws exception', () async {
        // Arrange
        when(() => mockSessionService.getToken())
            .thenThrow(Exception('Storage error'));

        // Act
        final result = await repository.isAuthenticated;

        // Assert
        expect(result, isFalse);
        verify(() => mockSessionService.getToken()).called(1);
      });

      test('fetches from storage when cache is null', () async {
        // Arrange
        when(() => mockSessionService.getToken())
            .thenAnswer((_) async => 'token_from_storage');

        // Act
        final result = await repository.isAuthenticated;

        // Assert
        expect(result, isTrue);
        verify(() => mockSessionService.getToken()).called(1);
      });
    });

    group('loginWithEmailAndPassword', () {
      const testEmail = '<EMAIL>';
      const testPassword = 'password123';
      const testToken = 'login_response_token';

      test('calls login service with correct credentials', () async {
        // Arrange
        final loginResponse = LoginResponse(token: testToken);
        when(() => mockLoginService.login(any()))
            .thenAnswer((_) async => loginResponse);
        when(() => mockSessionService.saveToken(any()))
            .thenAnswer((_) async {});
        when(() => mockLoginService.setAuthorizationHeader(any()))
            .thenReturn(null);

        // Act
        await repository.loginWithEmailAndPassword(
          email: testEmail,
          password: testPassword,
          storeToken: true,
        );

        // Assert
        verify(() => mockLoginService.login(any(
          that: isA<EmailPasswordCredentials>()
              .having((c) => c.email, 'email', testEmail)
              .having((c) => c.password, 'password', testPassword),
        ))).called(1);
      });

      test('sets authentication status to true after successful login', () async {
        // Arrange
        final loginResponse = LoginResponse(token: testToken);
        when(() => mockLoginService.login(any()))
            .thenAnswer((_) async => loginResponse);
        when(() => mockSessionService.saveToken(any()))
            .thenAnswer((_) async {});
        when(() => mockLoginService.setAuthorizationHeader(any()))
            .thenReturn(null);

        // Act
        await repository.loginWithEmailAndPassword(
          email: testEmail,
          password: testPassword,
          storeToken: true,
        );

        // Check authentication status (should be cached as true)
        final isAuthenticated = await repository.isAuthenticated;

        // Assert
        expect(isAuthenticated, isTrue);
        // getToken should not be called since status is cached
        verifyNever(() => mockSessionService.getToken());
      });

      test('saves token to session storage', () async {
        // Arrange
        final loginResponse = LoginResponse(token: testToken);
        when(() => mockLoginService.login(any()))
            .thenAnswer((_) async => loginResponse);
        when(() => mockSessionService.saveToken(any()))
            .thenAnswer((_) async {});
        when(() => mockLoginService.setAuthorizationHeader(any()))
            .thenReturn(null);

        // Act
        await repository.loginWithEmailAndPassword(
          email: testEmail,
          password: testPassword,
          storeToken: true,
        );

        // Assert
        verify(() => mockSessionService.saveToken(testToken)).called(1);
      });

      test('sets authorization header on login service', () async {
        // Arrange
        final loginResponse = LoginResponse(token: testToken);
        when(() => mockLoginService.login(any()))
            .thenAnswer((_) async => loginResponse);
        when(() => mockSessionService.saveToken(any()))
            .thenAnswer((_) async {});
        when(() => mockLoginService.setAuthorizationHeader(any()))
            .thenReturn(null);

        // Act
        await repository.loginWithEmailAndPassword(
          email: testEmail,
          password: testPassword,
          storeToken: true,
        );

        // Assert
        verify(() => mockLoginService.setAuthorizationHeader(testToken)).called(1);
      });

      test('ignores storage errors when saving token', () async {
        // Arrange
        final loginResponse = LoginResponse(token: testToken);
        when(() => mockLoginService.login(any()))
            .thenAnswer((_) async => loginResponse);
        when(() => mockSessionService.saveToken(any()))
            .thenThrow(Exception('Storage save error'));
        when(() => mockLoginService.setAuthorizationHeader(any()))
            .thenReturn(null);

        // Act & Assert - Should not throw
        await repository.loginWithEmailAndPassword(
          email: testEmail,
          password: testPassword,
          storeToken: true,
        );

        // Verify that other operations still completed
        verify(() => mockLoginService.setAuthorizationHeader(testToken)).called(1);
      });

      test('propagates login service exceptions', () async {
        // Arrange
        final loginException = Exception('Login failed');
        when(() => mockLoginService.login(any()))
            .thenThrow(loginException);

        // Act & Assert
        expect(
          () => repository.loginWithEmailAndPassword(
            email: testEmail,
            password: testPassword,
            storeToken: true,
          ),
          throwsA(equals(loginException)),
        );

        // Verify that subsequent operations were not called
        verifyNever(() => mockSessionService.saveToken(any()));
        verifyNever(() => mockLoginService.setAuthorizationHeader(any()));
      });

      test('handles empty email and password', () async {
        // Arrange
        final loginResponse = LoginResponse(token: testToken);
        when(() => mockLoginService.login(any()))
            .thenAnswer((_) async => loginResponse);
        when(() => mockSessionService.saveToken(any()))
            .thenAnswer((_) async {});
        when(() => mockLoginService.setAuthorizationHeader(any()))
            .thenReturn(null);

        // Act
        await repository.loginWithEmailAndPassword(
          email: '',
          password: '',
          storeToken: true,
        );

        // Assert
        verify(() => mockLoginService.login(any(
          that: isA<EmailPasswordCredentials>()
              .having((c) => c.email, 'email', '')
              .having((c) => c.password, 'password', ''),
        ))).called(1);
      });
    });

    group('integration scenarios', () {
      test('authentication status changes after login', () async {
        // Arrange
        const testEmail = '<EMAIL>';
        const testPassword = 'integrationPassword';
        const testToken = 'integration_token';
        
        // Initially no token
        when(() => mockSessionService.getToken())
            .thenAnswer((_) async => null);
        
        // Setup login
        final loginResponse = LoginResponse(token: testToken);
        when(() => mockLoginService.login(any()))
            .thenAnswer((_) async => loginResponse);
        when(() => mockSessionService.saveToken(any()))
            .thenAnswer((_) async {});
        when(() => mockLoginService.setAuthorizationHeader(any()))
            .thenReturn(null);

        // Act & Assert
        // Initially not authenticated
        final initialAuth = await repository.isAuthenticated;
        expect(initialAuth, isFalse);

        // Login
        await repository.loginWithEmailAndPassword(
          email: testEmail,
          password: testPassword,
          storeToken: true,
        );

        // Now authenticated (cached)
        final finalAuth = await repository.isAuthenticated;
        expect(finalAuth, isTrue);
      });
    });

    group('logout', () {
      test('removes authorization header and deletes token from storage', () async {
        // Arrange
        when(() => mockLoginService.removeAuthorizationHeader()).thenReturn(null);
        when(() => mockSessionService.removeToken()).thenAnswer((_) async {});

        // Act
        await repository.logout();

        // Assert
        verify(() => mockLoginService.removeAuthorizationHeader()).called(1);
        verify(() => mockSessionService.removeToken()).called(1);
      });

      test('sets authentication status to false and uses cache without hitting storage', () async {
        // Arrange: prime cache as authenticated first
        when(() => mockSessionService.getToken()).thenAnswer((_) async => 'cached_token');
        final beforeLogout = await repository.isAuthenticated; // populates cache = true
        expect(beforeLogout, isTrue);

        // Arrange for logout
        when(() => mockLoginService.removeAuthorizationHeader()).thenReturn(null);
        when(() => mockSessionService.removeToken()).thenAnswer((_) async {});

        // Act
        await repository.logout();

        // After logout, isAuthenticated should return cached false without calling storage again
        final afterLogout = await repository.isAuthenticated;

        // Assert
        expect(afterLogout, isFalse);
        // getToken should have been called only once (during the initial cache prime)
        verify(() => mockSessionService.getToken()).called(1);
      });

      test('ignores storage errors when removing token', () async {
        // Arrange
        when(() => mockLoginService.removeAuthorizationHeader()).thenReturn(null);
        when(() => mockSessionService.removeToken()).thenThrow(Exception('remove error'));

        // Act & Assert - Should not throw
        await repository.logout();

        // Verify header removal still executed
        verify(() => mockLoginService.removeAuthorizationHeader()).called(1);
      });
    });
  });
}
