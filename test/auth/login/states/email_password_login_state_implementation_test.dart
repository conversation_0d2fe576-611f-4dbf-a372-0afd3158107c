import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:zuz_app/auth/login/repositories/login_repository.dart';
import 'package:zuz_app/auth/login/models/login_strings.dart';
import 'package:zuz_app/auth/login/states/email_password_login_state_implementation.dart';
import 'package:zuz_app/error/models/app_error.dart';
import 'package:zuz_app/error/state/error_handler_state.dart';
import 'package:zuz_app/locator/locator.dart';
import 'package:zuz_app/localization/models/strings.dart';
import 'package:zuz_app/localization/repositories/locale_repository.dart';
import 'package:zuz_app/navigation/models/onboarding_route.dart';
import 'package:zuz_app/navigation/repositories/navigation_repository.dart';

class MockLoginRepository extends Mock implements LoginRepository {}
class MockLocaleRepository extends Mock implements LocaleRepository {}
class MockNavigationRepository extends Mock implements NavigationRepository {}
class MockStrings extends Mock implements Strings {}
class MockLoginStrings extends Mock implements LoginStrings {}
class MockAppError extends Mock implements AppError {}
class FakePasswordResetRoute extends Fake implements PasswordResetRoute {}
class MockErrorHandlerState extends Mock implements ErrorHandlerState {}

void main() {
  setUpAll(() {
    registerFallbackValue(MockBuildContext());
    registerFallbackValue(FakePasswordResetRoute());
  });
  group('EmailPasswordLoginStateImplementation', () {
    late MockLoginRepository mockLoginRepository;
    late MockLocaleRepository mockLocaleRepository;
    late MockNavigationRepository mockNavigationRepository;
    late MockStrings mockStrings;
    late MockLoginStrings mockLoginStrings;
    late EmailPasswordLoginStateImplementation stateNotifier;

    setUp(() async {
      // Reset and register ErrorHandlerState used by AsyncAction
      await Locator.instance.reset();
      final mockErrorHandler = MockErrorHandlerState();
      when(() => mockErrorHandler.showMessage(any())).thenReturn(null);
      Locator.instance.registerSingleton<ErrorHandlerState>(mockErrorHandler);

      mockLoginRepository = MockLoginRepository();
      mockLocaleRepository = MockLocaleRepository();
      mockNavigationRepository = MockNavigationRepository();
      mockStrings = MockStrings();
      mockLoginStrings = MockLoginStrings();

      // Setup default mocks
      when(() => mockLocaleRepository.strings).thenReturn(mockStrings);
      when(() => mockStrings.loginStrings).thenReturn(mockLoginStrings);
      when(() => mockLoginStrings.invalidEmailError).thenReturn('E-mail inválido');
      when(() => mockLoginStrings.invalidPasswordError).thenReturn('Senha inválida');

      stateNotifier = EmailPasswordLoginStateImplementation(
        loginRepository: mockLoginRepository,
        localeRepository: mockLocaleRepository,
        navigationRepository: mockNavigationRepository,
      );
    });

    tearDown(() async {
      await Locator.instance.reset();
    });

    group('constructor', () {
      test('creates instance with required dependencies', () {
        // Act
        final notifier = EmailPasswordLoginStateImplementation(
          loginRepository: mockLoginRepository,
          localeRepository: mockLocaleRepository,
          navigationRepository: mockNavigationRepository,
        );

        // Assert
        expect(notifier, isA<EmailPasswordLoginStateImplementation>());
        expect(notifier, isA<ChangeNotifier>());
      });
    });

    group('initial state', () {
      test('has correct initial values', () {
        // Assert
        expect(stateNotifier.email, equals(''));
        expect(stateNotifier.password, equals(''));
        expect(stateNotifier.loginAction.isRunning, isFalse);
        expect(stateNotifier.autovalidateMode, equals(AutovalidateMode.disabled));
        expect(stateNotifier.formKey, isA<GlobalKey<FormState>>());
      });

      test('returns login strings from locale repository', () {
        // Act
        final strings = stateNotifier.strings;

        // Assert
        expect(strings, equals(mockLoginStrings));
        verify(() => mockLocaleRepository.strings).called(1);
        verify(() => mockStrings.loginStrings).called(1);
      });
    });

    group('onEmailChanged', () {
      test('updates email value', () {
        // Arrange
        const testEmail = '<EMAIL>';

        // Act
        stateNotifier.onEmailChanged(testEmail);

        // Assert
        expect(stateNotifier.email, equals(testEmail));
      });

      test('handles empty email', () {
        // Act
        stateNotifier.onEmailChanged('');

        // Assert
        expect(stateNotifier.email, equals(''));
      });

      test('handles special characters in email', () {
        // Arrange
        const specialEmail = '<EMAIL>';

        // Act
        stateNotifier.onEmailChanged(specialEmail);

        // Assert
        expect(stateNotifier.email, equals(specialEmail));
      });
    });

    group('onPasswordChanged', () {
      test('updates password value', () {
        // Arrange
        const testPassword = 'password123';

        // Act
        stateNotifier.onPasswordChanged(testPassword);

        // Assert
        expect(stateNotifier.password, equals(testPassword));
      });

      test('handles empty password', () {
        // Act
        stateNotifier.onPasswordChanged('');

        // Assert
        expect(stateNotifier.password, equals(''));
      });

      test('handles special characters in password', () {
        // Arrange
        const specialPassword = 'p@ssw0rd!@#\$%^&*()';

        // Act
        stateNotifier.onPasswordChanged(specialPassword);

        // Assert
        expect(stateNotifier.password, equals(specialPassword));
      });
    });

    group('validateEmail', () {
      test('returns null for valid email', () {
        // Arrange
        const validEmail = '<EMAIL>';

        // Act
        final result = stateNotifier.validateEmail(validEmail);

        // Assert
        expect(result, isNull);
      });

      test('returns error message for null email', () {
        // Act
        final result = stateNotifier.validateEmail(null);

        // Assert
        expect(result, equals('E-mail inválido'));
        verify(() => mockLoginStrings.invalidEmailError).called(1);
      });

      test('returns error message for empty email', () {
        // Act
        final result = stateNotifier.validateEmail('');

        // Assert
        expect(result, equals('E-mail inválido'));
        verify(() => mockLoginStrings.invalidEmailError).called(1);
      });

      test('returns null for whitespace email', () {
        // Act
        final result = stateNotifier.validateEmail(' ');

        // Assert
        expect(result, isNull);
      });
    });

    group('validatePassword', () {
      test('returns null for valid password', () {
        // Arrange
        const validPassword = 'password123';

        // Act
        final result = stateNotifier.validatePassword(validPassword);

        // Assert
        expect(result, isNull);
      });

      test('returns error message for null password', () {
        // Act
        final result = stateNotifier.validatePassword(null);

        // Assert
        expect(result, equals('Senha inválida'));
        verify(() => mockLoginStrings.invalidPasswordError).called(1);
      });

      test('returns error message for empty password', () {
        // Act
        final result = stateNotifier.validatePassword('');

        // Assert
        expect(result, equals('Senha inválida'));
        verify(() => mockLoginStrings.invalidPasswordError).called(1);
      });

      test('returns null for whitespace password', () {
        // Act
        final result = stateNotifier.validatePassword(' ');

        // Assert
        expect(result, isNull);
      });
    });

    group('loginAction', () {
      testWidgets('enables autovalidate mode and notifies listeners when executed', (tester) async {
        // Arrange
        var notificationCount = 0;
        stateNotifier.addListener(() => notificationCount++);

        // Create a test widget with form
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Form(
                key: stateNotifier.formKey,
                child: Column(
                  children: [
                    TextFormField(
                      validator: stateNotifier.validateEmail,
                    ),
                    TextFormField(
                      validator: stateNotifier.validatePassword,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );

        // Act
        await stateNotifier.loginAction.execute();

        // Assert
        expect(stateNotifier.autovalidateMode, equals(AutovalidateMode.always));
        expect(notificationCount, greaterThan(0));
      });

      testWidgets('does not call login repository when form validation fails', (tester) async {
        // Arrange
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Form(
                key: stateNotifier.formKey,
                child: Column(
                  children: [
                    TextFormField(
                      validator: stateNotifier.validateEmail,
                    ),
                    TextFormField(
                      validator: stateNotifier.validatePassword,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );

        // Act - with empty email and password (invalid)
        await stateNotifier.loginAction.execute();

        // Assert
        verifyNever(() => mockLoginRepository.loginWithEmailAndPassword(
          email: any(named: 'email'),
          password: any(named: 'password'),
          storeToken: any(named: 'storeToken'),
        ));
      });

      testWidgets('calls login repository when form validation passes', (tester) async {
        // Arrange
        stateNotifier.onEmailChanged('<EMAIL>');
        stateNotifier.onPasswordChanged('password123');
        
        when(() => mockLoginRepository.loginWithEmailAndPassword(
          email: any(named: 'email'),
          password: any(named: 'password'),
          storeToken: any(named: 'storeToken'),
        )).thenAnswer((_) async {});

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Form(
                key: stateNotifier.formKey,
                child: Column(
                  children: [
                    TextFormField(
                      validator: stateNotifier.validateEmail,
                      initialValue: '<EMAIL>',
                    ),
                    TextFormField(
                      validator: stateNotifier.validatePassword,
                      initialValue: 'password123',
                    ),
                  ],
                ),
              ),
            ),
          ),
        );

        // Act
        await stateNotifier.loginAction.execute();

        // Assert
        verify(() => mockLoginRepository.loginWithEmailAndPassword(
          email: '<EMAIL>',
          password: 'password123',
          storeToken: true,
        )).called(1);
      });
    });

    group('login flow', () {
      test('sets isRunning to true during login', () async {
        // Arrange
        stateNotifier.onEmailChanged('<EMAIL>');
        stateNotifier.onPasswordChanged('password123');

        var isRunningStates = <bool>[];
        stateNotifier.loginAction.addListener(() {
          isRunningStates.add(stateNotifier.loginAction.isRunning);
        });

        when(() => mockLoginRepository.loginWithEmailAndPassword(
          email: any(named: 'email'),
          password: any(named: 'password'),
          storeToken: any(named: 'storeToken'),
        )).thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 10));
        });

        // Act
        final loginFuture = stateNotifier.loginAction.execute();
        
        // Assert - Check that isRunning is true during execution
        expect(stateNotifier.loginAction.isRunning, isTrue);
        
        await loginFuture;
        
        // Assert - Check that isRunning is false after completion
        expect(stateNotifier.loginAction.isRunning, isFalse);
        expect(isRunningStates, isNotEmpty);
      });

      testWidgets('calls login repository with correct credentials', (tester) async {
        // Arrange
        const testEmail = '<EMAIL>';
        const testPassword = 'password123';

        stateNotifier.onEmailChanged(testEmail);
        stateNotifier.onPasswordChanged(testPassword);

        when(() => mockLoginRepository.loginWithEmailAndPassword(
          email: any(named: 'email'),
          password: any(named: 'password'),
          storeToken: any(named: 'storeToken'),
        )).thenAnswer((_) async {});

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Form(
                key: stateNotifier.formKey,
                child: Column(
                  children: [
                    TextFormField(
                      validator: stateNotifier.validateEmail,
                      initialValue: testEmail,
                    ),
                    TextFormField(
                      validator: stateNotifier.validatePassword,
                      initialValue: testPassword,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );

        // Act
        await stateNotifier.loginAction.execute();

        // Assert
        verify(() => mockLoginRepository.loginWithEmailAndPassword(
          email: testEmail,
          password: testPassword,
          storeToken: true,
        )).called(1);
      });

      testWidgets('handles AppError during login', (tester) async {
        // Arrange
        stateNotifier.onEmailChanged('<EMAIL>');
        stateNotifier.onPasswordChanged('password123');

        final mockError = MockAppError();
        when(() => mockLoginRepository.loginWithEmailAndPassword(
          email: any(named: 'email'),
          password: any(named: 'password'),
          storeToken: any(named: 'storeToken'),
        )).thenThrow(mockError);

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Form(
                key: stateNotifier.formKey,
                child: Column(
                  children: [
                    TextFormField(
                      validator: stateNotifier.validateEmail,
                      initialValue: '<EMAIL>',
                    ),
                    TextFormField(
                      validator: stateNotifier.validatePassword,
                      initialValue: 'password123',
                    ),
                  ],
                ),
              ),
            ),
          ),
        );

        // Act & Assert - Should not throw
        await stateNotifier.loginAction.execute();

        // Assert that isRunning is reset to false even after error
        expect(stateNotifier.loginAction.isRunning, isFalse);
      });

      testWidgets('resets isRunning to false after login completes', (tester) async {
        // Arrange
        stateNotifier.onEmailChanged('<EMAIL>');
        stateNotifier.onPasswordChanged('password123');

        when(() => mockLoginRepository.loginWithEmailAndPassword(
          email: any(named: 'email'),
          password: any(named: 'password'),
          storeToken: any(named: 'storeToken'),
        )).thenAnswer((_) async {});

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Form(
                key: stateNotifier.formKey,
                child: Column(
                  children: [
                    TextFormField(
                      validator: stateNotifier.validateEmail,
                      initialValue: '<EMAIL>',
                    ),
                    TextFormField(
                      validator: stateNotifier.validatePassword,
                      initialValue: 'password123',
                    ),
                  ],
                ),
              ),
            ),
          ),
        );

        // Act
        await stateNotifier.loginAction.execute();

        // Assert
        expect(stateNotifier.loginAction.isRunning, isFalse);
      });
    });

    group('listener notifications', () {
      test('notifies listeners when autovalidate mode changes', () async {
        // Arrange
        var notificationCount = 0;
        stateNotifier.addListener(() => notificationCount++);

        // Act
        await stateNotifier.loginAction.execute();

        // Assert
        expect(notificationCount, greaterThan(0));
      });

      test('notifies listeners during login state changes', () async {
        // Arrange
        var notificationCount = 0;
        stateNotifier.addListener(() => notificationCount++);

        // Act - Just test the autovalidate mode change notification
        await stateNotifier.loginAction.execute();

        // Assert - Should have at least one notification for autovalidate mode change
        expect(notificationCount, greaterThan(0));
      });
    });

    group('resetPassword', () {
      test('calls navigation repository to navigate to password reset route', () {
        // Act
        stateNotifier.resetPassword();

        // Assert
        verify(() => mockNavigationRepository.to(any())).called(1);
      });

      test('navigates to PasswordResetRoute specifically', () {
        // Act
        stateNotifier.resetPassword();

        // Assert
        final captured = verify(() => mockNavigationRepository.to(captureAny()));
        captured.called(1);
        final route = captured.captured.single;
        expect(route, isA<PasswordResetRoute>());
      });
    });
  });
}

class MockBuildContext extends Mock implements BuildContext {}
