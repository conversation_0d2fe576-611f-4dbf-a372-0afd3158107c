import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:zuz_app/auth/login/models/email_password_credentials.dart';
import 'package:zuz_app/auth/login/models/login_error.dart';
import 'package:zuz_app/auth/login/models/login_response.dart';
import 'package:zuz_app/auth/login/services/email_password_login_service_implementation.dart';
import 'package:zuz_app/http_client/models/http_error.dart';
import 'package:zuz_app/http_client/models/http_method.dart';
import 'package:zuz_app/http_client/services/http_client_service.dart';

class MockHttpClientService extends Mock implements HttpClientService {}

class FakeEmailPasswordCredentials extends Fake implements EmailPasswordCredentials {}

void main() {
  setUpAll(() {
    registerFallbackValue(FakeEmailPasswordCredentials());
    registerFallbackValue(HttpMethod.get);
  });
  group('EmailPasswordLoginServiceImplementation', () {
    late MockHttpClientService mockHttpClient;
    late EmailPasswordLoginServiceImplementation loginService;

    setUp(() {
      mockHttpClient = MockHttpClientService();
      loginService = EmailPasswordLoginServiceImplementation(httpClient: mockHttpClient);
    });

    group('constructor', () {
      test('creates instance with required httpClient parameter', () {
        // Act
        final service = EmailPasswordLoginServiceImplementation(httpClient: mockHttpClient);

        // Assert
        expect(service, isA<EmailPasswordLoginServiceImplementation>());
      });
    });

    group('login', () {
      const testEmail = '<EMAIL>';
      const testPassword = 'password123';
      const testToken = 'test_token_abc123';

      test('makes POST request to correct endpoint with credentials', () async {
        // Arrange
        final credentials = EmailPasswordCredentials(
          email: testEmail,
          password: testPassword,
        );
        final responseData = {'token': testToken};
        
        when(() => mockHttpClient.request(
          url: any(named: 'url'),
          method: any(named: 'method'),
          body: any(named: 'body'),
        )).thenAnswer((_) async => responseData);

        // Act
        await loginService.login(credentials);

        // Assert
        verify(() => mockHttpClient.request(
          url: '/auth/signin',
          method: HttpMethod.post,
          body: credentials.toJson(),
        )).called(1);
      });

      test('returns LoginResponse on successful login', () async {
        // Arrange
        final credentials = EmailPasswordCredentials(
          email: testEmail,
          password: testPassword,
        );
        final responseData = {'token': testToken};
        
        when(() => mockHttpClient.request(
          url: any(named: 'url'),
          method: any(named: 'method'),
          body: any(named: 'body'),
        )).thenAnswer((_) async => responseData);

        // Act
        final result = await loginService.login(credentials);

        // Assert
        expect(result, isA<LoginResponse>());
        expect(result.token, equals(testToken));
      });

      test('throws LoginError.credentialsNotFound on HttpError.badRequest', () async {
        // Arrange
        final credentials = EmailPasswordCredentials(
          email: testEmail,
          password: testPassword,
        );
        
        when(() => mockHttpClient.request(
          url: any(named: 'url'),
          method: any(named: 'method'),
          body: any(named: 'body'),
        )).thenThrow(HttpError.badRequest);

        // Act & Assert
        expect(
          () => loginService.login(credentials),
          throwsA(isA<LoginError>().having(
            (e) => e,
            'error',
            LoginError.credentialsNotFound,
          )),
        );
      });

      test('throws LoginError.credentialsNotFound on HttpError.notFound', () async {
        // Arrange
        final credentials = EmailPasswordCredentials(
          email: testEmail,
          password: testPassword,
        );
        
        when(() => mockHttpClient.request(
          url: any(named: 'url'),
          method: any(named: 'method'),
          body: any(named: 'body'),
        )).thenThrow(HttpError.notFound);

        // Act & Assert
        expect(
          () => loginService.login(credentials),
          throwsA(isA<LoginError>().having(
            (e) => e,
            'error',
            LoginError.credentialsNotFound,
          )),
        );
      });

      test('throws LoginError.unknown on HttpError.server', () async {
        // Arrange
        final credentials = EmailPasswordCredentials(
          email: testEmail,
          password: testPassword,
        );
        
        when(() => mockHttpClient.request(
          url: any(named: 'url'),
          method: any(named: 'method'),
          body: any(named: 'body'),
        )).thenThrow(HttpError.server);

        // Act & Assert
        expect(
          () => loginService.login(credentials),
          throwsA(isA<LoginError>().having(
            (e) => e,
            'error',
            LoginError.unknown,
          )),
        );
      });

      test('throws LoginError.unknown on HttpError.connection', () async {
        // Arrange
        final credentials = EmailPasswordCredentials(
          email: testEmail,
          password: testPassword,
        );
        
        when(() => mockHttpClient.request(
          url: any(named: 'url'),
          method: any(named: 'method'),
          body: any(named: 'body'),
        )).thenThrow(HttpError.connection);

        // Act & Assert
        expect(
          () => loginService.login(credentials),
          throwsA(isA<LoginError>().having(
            (e) => e,
            'error',
            LoginError.unknown,
          )),
        );
      });

      test('throws LoginError.unknown on HttpError.unauthorized', () async {
        // Arrange
        final credentials = EmailPasswordCredentials(
          email: testEmail,
          password: testPassword,
        );
        
        when(() => mockHttpClient.request(
          url: any(named: 'url'),
          method: any(named: 'method'),
          body: any(named: 'body'),
        )).thenThrow(HttpError.unauthorized);

        // Act & Assert
        expect(
          () => loginService.login(credentials),
          throwsA(isA<LoginError>().having(
            (e) => e,
            'error',
            LoginError.unknown,
          )),
        );
      });

      test('handles empty credentials', () async {
        // Arrange
        final credentials = EmailPasswordCredentials(
          email: '',
          password: '',
        );
        final responseData = {'token': testToken};
        
        when(() => mockHttpClient.request(
          url: any(named: 'url'),
          method: any(named: 'method'),
          body: any(named: 'body'),
        )).thenAnswer((_) async => responseData);

        // Act
        final result = await loginService.login(credentials);

        // Assert
        expect(result.token, equals(testToken));
        verify(() => mockHttpClient.request(
          url: '/auth/signin',
          method: HttpMethod.post,
          body: {'email': '', 'password': ''},
        )).called(1);
      });
    });

    group('setAuthorizationHeader', () {
      test('calls httpClient.setAuthorizationHeader with token', () {
        // Arrange
        const testToken = 'bearer_token_xyz789';
        when(() => mockHttpClient.setAuthorizationHeader(any())).thenReturn(null);

        // Act
        loginService.setAuthorizationHeader(testToken);

        // Assert
        verify(() => mockHttpClient.setAuthorizationHeader(testToken)).called(1);
      });

      test('handles empty token', () {
        // Arrange
        const emptyToken = '';
        when(() => mockHttpClient.setAuthorizationHeader(any())).thenReturn(null);

        // Act
        loginService.setAuthorizationHeader(emptyToken);

        // Assert
        verify(() => mockHttpClient.setAuthorizationHeader(emptyToken)).called(1);
      });

      test('handles special characters in token', () {
        // Arrange
        const specialToken = 'token.with-special_chars@123!';
        when(() => mockHttpClient.setAuthorizationHeader(any())).thenReturn(null);

        // Act
        loginService.setAuthorizationHeader(specialToken);

        // Assert
        verify(() => mockHttpClient.setAuthorizationHeader(specialToken)).called(1);
      });
    });

    group('removeAuthorizationHeader', () {
      test('calls httpClient.removeAuthorizationHeader', () {
        // Arrange
        when(() => mockHttpClient.removeAuthorizationHeader()).thenReturn(null);

        // Act
        loginService.removeAuthorizationHeader();

        // Assert
        verify(() => mockHttpClient.removeAuthorizationHeader()).called(1);
      });

      test('can be called multiple times without throwing', () {
        // Arrange
        when(() => mockHttpClient.removeAuthorizationHeader()).thenReturn(null);

        // Act
        loginService.removeAuthorizationHeader();
        loginService.removeAuthorizationHeader();

        // Assert
        verify(() => mockHttpClient.removeAuthorizationHeader()).called(2);
      });
    });
  });
}
