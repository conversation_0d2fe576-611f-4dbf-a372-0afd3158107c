import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:zuz_app/auth/login/services/session_storage_service_implementation.dart';
import 'package:zuz_app/local_storage/models/local_storage_key.dart';
import 'package:zuz_app/local_storage/services/local_storage_service.dart';

class MockLocalStorageService extends Mock implements LocalStorageService {}

void main() {
  setUpAll(() {
    registerFallbackValue(LocalStorageKey.sessionToken);
  });
  group('SessionStorageServiceImplementation', () {
    late MockLocalStorageService mockLocalStorageService;
    late SessionStorageServiceImplementation sessionStorage;

    setUp(() {
      mockLocalStorageService = MockLocalStorageService();
      sessionStorage = SessionStorageServiceImplementation(localStorage: mockLocalStorageService);
    });

    group('constructor', () {
      test('creates instance with required localStorage parameter', () {
        // Act
        final storage = SessionStorageServiceImplementation(localStorage: mockLocalStorageService);

        // Assert
        expect(storage, isA<SessionStorageServiceImplementation>());
      });
    });

    group('saveToken', () {
      test('calls localStorage.setValue with correct parameters', () async {
        // Arrange
        const testToken = 'test_token_123';
        when(() => mockLocalStorageService.setValue(
          LocalStorageKey.sessionToken,
          testToken,
        )).thenAnswer((_) async {});

        // Act
        await sessionStorage.saveToken(testToken);

        // Assert
        verify(() => mockLocalStorageService.setValue(
          LocalStorageKey.sessionToken,
          testToken,
        )).called(1);
      });

      test('handles empty token', () async {
        // Arrange
        const emptyToken = '';

        // Act
        await sessionStorage.saveToken(emptyToken);

        // Assert
        verify(() => mockLocalStorageService.setValue(
          LocalStorageKey.sessionToken,
          emptyToken,
        )).called(1);
      });

      test('propagates localStorage exceptions', () async {
        // Arrange
        const testToken = 'test_token_123';
        final exception = Exception('Storage error');
        when(() => mockLocalStorageService.setValue(any(), any()))
            .thenThrow(exception);

        // Act & Assert
        expect(
          () => sessionStorage.saveToken(testToken),
          throwsA(equals(exception)),
        );
      });

      test('handles special characters in token', () async {
        // Arrange
        const specialToken = 'token.with-special_chars@123!';

        // Act
        await sessionStorage.saveToken(specialToken);

        // Assert
        verify(() => mockLocalStorageService.setValue(
          LocalStorageKey.sessionToken,
          specialToken,
        )).called(1);
      });
    });

    group('getToken', () {
      test('calls localStorage.getValue with correct parameters and returns token', () async {
        // Arrange
        const expectedToken = 'stored_token_456';
        when(() => mockLocalStorageService.getValue<String>(any()))
            .thenAnswer((_) async => expectedToken);

        // Act
        final result = await sessionStorage.getToken();

        // Assert
        expect(result, equals(expectedToken));
        verify(() => mockLocalStorageService.getValue<String>(
          LocalStorageKey.sessionToken,
        )).called(1);
      });

      test('returns null when no token is stored', () async {
        // Arrange
        when(() => mockLocalStorageService.getValue<String>(any()))
            .thenAnswer((_) async => null);

        // Act
        final result = await sessionStorage.getToken();

        // Assert
        expect(result, isNull);
        verify(() => mockLocalStorageService.getValue<String>(
          LocalStorageKey.sessionToken,
        )).called(1);
      });

      test('returns empty string when empty token is stored', () async {
        // Arrange
        when(() => mockLocalStorageService.getValue<String>(any()))
            .thenAnswer((_) async => '');

        // Act
        final result = await sessionStorage.getToken();

        // Assert
        expect(result, equals(''));
      });

      test('propagates localStorage exceptions', () async {
        // Arrange
        final exception = Exception('Storage read error');
        when(() => mockLocalStorageService.getValue<String>(any()))
            .thenThrow(exception);

        // Act & Assert
        expect(
          () => sessionStorage.getToken(),
          throwsA(equals(exception)),
        );
      });
    });

    group('removeToken', () {
      test('calls localStorage.removeValue with correct parameters', () async {
        // Act
        await sessionStorage.removeToken();

        // Assert
        verify(() => mockLocalStorageService.removeValue(
          LocalStorageKey.sessionToken,
        )).called(1);
      });

      test('propagates localStorage exceptions', () async {
        // Arrange
        final exception = Exception('Storage remove error');
        when(() => mockLocalStorageService.removeValue(any()))
            .thenThrow(exception);

        // Act & Assert
        expect(
          () => sessionStorage.removeToken(),
          throwsA(equals(exception)),
        );
      });
    });

    group('integration scenarios', () {
      test('save and retrieve token workflow', () async {
        // Arrange
        const testToken = 'integration_test_token';
        when(() => mockLocalStorageService.getValue<String>(any()))
            .thenAnswer((_) async => testToken);

        // Act
        await sessionStorage.saveToken(testToken);
        final retrievedToken = await sessionStorage.getToken();

        // Assert
        expect(retrievedToken, equals(testToken));
        verify(() => mockLocalStorageService.setValue(
          LocalStorageKey.sessionToken,
          testToken,
        )).called(1);
        verify(() => mockLocalStorageService.getValue<String>(
          LocalStorageKey.sessionToken,
        )).called(1);
      });

      test('save, remove, and retrieve token workflow', () async {
        // Arrange
        const testToken = 'token_to_be_removed';
        // Override the default getValue stub for this test
        when(() => mockLocalStorageService.getValue<String>(any()))
            .thenAnswer((_) async => null);

        // Act
        await sessionStorage.saveToken(testToken);
        await sessionStorage.removeToken();
        final retrievedToken = await sessionStorage.getToken();

        // Assert
        expect(retrievedToken, isNull);
        verify(() => mockLocalStorageService.setValue(
          LocalStorageKey.sessionToken,
          testToken,
        )).called(1);
        verify(() => mockLocalStorageService.removeValue(
          LocalStorageKey.sessionToken,
        )).called(1);
        verify(() => mockLocalStorageService.getValue<String>(
          LocalStorageKey.sessionToken,
        )).called(1);
      });
    });
  });
}
