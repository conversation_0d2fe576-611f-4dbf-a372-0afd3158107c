import 'package:flutter_test/flutter_test.dart';
import 'package:zuz_app/locator/locator.dart';

// Test classes for registration
class TestService {
  final String name;
  TestService(this.name);
}

class AnotherTestService {
  final int value;
  AnotherTestService(this.value);
}

void main() {
  late Locator locator;

  setUp(() async {
    locator = Locator.instance;
    await locator.reset(); // Clear any previous registrations
  });

  group('Locator', () {
    group('singleton instance', () {
      test('returns the same instance every time', () {
        // Act
        final instance1 = Locator.instance;
        final instance2 = Locator.instance;

        // Assert
        expect(instance1, same(instance2));
      });
    });

    group('registerSingleton', () {
      test('registers a singleton instance', () {
        // Arrange
        final testService = TestService('test');

        // Act
        locator.registerSingleton<TestService>(testService);

        // Assert
        expect(locator.isRegistered<TestService>(), isTrue);
        expect(locator.get<TestService>(), same(testService));
      });

      test('returns the same instance on multiple get calls', () {
        // Arrange
        final testService = TestService('test');
        locator.registerSingleton<TestService>(testService);

        // Act
        final instance1 = locator.get<TestService>();
        final instance2 = locator.get<TestService>();

        // Assert
        expect(instance1, same(instance2));
        expect(instance1, same(testService));
      });
    });

    group('registerLazySingleton', () {
      test('registers a lazy singleton factory', () {
        // Arrange
        TestService factory() => TestService('lazy-test');

        // Act
        locator.registerLazySingleton<TestService>(factory);

        // Assert
        expect(locator.isRegistered<TestService>(), isTrue);
      });

      test('creates instance only on first get call', () {
        // Arrange
        var factoryCalled = false;
        TestService factory() {
          factoryCalled = true;
          return TestService('lazy-test');
        }
        locator.registerLazySingleton<TestService>(factory);

        // Assert factory not called yet
        expect(factoryCalled, isFalse);

        // Act - first get call
        final instance1 = locator.get<TestService>();

        // Assert factory was called
        expect(factoryCalled, isTrue);
        expect(instance1.name, equals('lazy-test'));

        // Act - second get call
        final instance2 = locator.get<TestService>();

        // Assert same instance returned
        expect(instance1, same(instance2));
      });
    });

    group('get', () {
      test('throws StateError when type is not registered', () {
        // Act & Assert
        expect(
          () => locator.get<TestService>(),
          throwsA(isA<StateError>().having(
            (e) => e.message,
            'message',
            contains('Type TestService is not registered'),
          )),
        );
      });
    });

    group('isRegistered', () {
      test('returns false for unregistered types', () {
        // Act & Assert
        expect(locator.isRegistered<TestService>(), isFalse);
      });

      test('returns true for registered singleton', () {
        // Arrange
        locator.registerSingleton<TestService>(TestService('test'));

        // Act & Assert
        expect(locator.isRegistered<TestService>(), isTrue);
      });

      test('returns true for registered lazy singleton', () {
        // Arrange
        locator.registerLazySingleton<TestService>(() => TestService('test'));

        // Act & Assert
        expect(locator.isRegistered<TestService>(), isTrue);
      });
    });

    group('multiple types', () {
      test('can register and retrieve different types', () {
        // Arrange
        final testService = TestService('test');
        final anotherService = AnotherTestService(42);

        // Act
        locator.registerSingleton<TestService>(testService);
        locator.registerSingleton<AnotherTestService>(anotherService);

        // Assert
        expect(locator.get<TestService>(), same(testService));
        expect(locator.get<AnotherTestService>(), same(anotherService));
        expect(locator.isRegistered<TestService>(), isTrue);
        expect(locator.isRegistered<AnotherTestService>(), isTrue);
      });
    });

    group('reset', () {
      test('clears all registrations', () async {
        // Arrange
        locator.registerSingleton<TestService>(TestService('test'));
        locator.registerSingleton<AnotherTestService>(AnotherTestService(42));
        expect(locator.isRegistered<TestService>(), isTrue);
        expect(locator.isRegistered<AnotherTestService>(), isTrue);

        // Act
        await locator.reset();

        // Assert
        expect(locator.isRegistered<TestService>(), isFalse);
        expect(locator.isRegistered<AnotherTestService>(), isFalse);
      });
    });
  });
}
