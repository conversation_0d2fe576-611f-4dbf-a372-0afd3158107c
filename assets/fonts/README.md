# Fontes do Projeto

Este diretório contém as fontes utilizadas no aplicativo.

## Century Gothic

A fonte Century Gothic é utilizada em todo o projeto conforme definido no Design System.

### Arquivos necessários:
- `CenturyGothic.ttf` - Peso normal (400)
- `CenturyGothic-Bold.ttf` - Peso bold (700)

### Como obter:
1. A fonte Century Gothic está disponível no Windows por padrão
2. Você pode encontrá-la em: `C:\Windows\Fonts\`
3. Copie os arquivos:
   - `CenturyGothic.ttf`
   - `CenturyGothic-Bold.ttf`
4. <PERSON>-<PERSON>s neste diretório (`assets/fonts/`)

### Configuração:
A fonte já está configurada no `pubspec.yaml` e no tema global da aplicação em `lib/theme/app_theme.dart`.

### Uso:
Não é necessário especificar `fontFamily` nos componentes, pois a fonte está configurada globalmente no tema.

```dart
// ❌ Não faça isso:
Text('Exemplo', style: TextStyle(fontFamily: 'Century Gothic'))

// ✅ Faça assim:
Text('Exemplo', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w700))
```

O tema automaticamente aplicará a fonte Century Gothic em todos os textos do aplicativo.
